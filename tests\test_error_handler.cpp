#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QFile>
#include <QTimer>
#include <QEventLoop>
#include <QException>
#include <QMessageBox>
#include <QString>

#include "ErrorHandler.h"
#include "Logger.h"

class TestException : public QException
{
public:
    TestException(const QString& message) : m_message(message) {}
    const char* what() const noexcept override { return m_message.toUtf8().constData(); }
    void raise() const override { throw *this; }
    TestException* clone() const override { return new TestException(*this); }

private:
    QString m_message;
};

class TestErrorHandler : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Singleton tests
    void testSingletonInstance();
    void testSingletonThreadSafety();

    // Basic error reporting tests
    void testBasicErrorReporting();
    void testErrorSeverityLevels();
    void testErrorCategories();
    void testErrorSignalEmission();
    void testCriticalErrorHandling();

    // Error information tests
    void testErrorInfoStructure();
    void testErrorTimestamp();
    void testErrorLocation();
    void testErrorDetails();

    // Configuration tests
    void testUserNotificationConfiguration();
    void testAutoReportingConfiguration();
    void testMaxErrorHistoryConfiguration();

    // Error history tests
    void testErrorHistoryStorage();
    void testErrorHistoryTrimming();
    void testErrorHistoryRetrieval();
    void testErrorHistoryClear();
    void testErrorCountBySeverity();

    // Exception handling tests
    void testExceptionReporting();
    void testSystemErrorReporting();
    void testFileErrorReporting();
    void testPdfErrorReporting();

    // Auto-recovery tests
    void testAutoRecoveryDetection();
    void testAutoRecoveryAttempt();
    void testAutoRecoverySignals();

    // User notification tests
    void testUserNotificationEnabled();
    void testUserNotificationDisabled();
    void testNotificationDelay();

    // Qt message handling tests
    void testQtMessageHandling();
    void testQtDebugMessages();
    void testQtWarningMessages();
    void testQtCriticalMessages();

    // Recovery suggestion tests
    void testRecoverySuggestions();
    void testRecoverySuggestionsByCategory();

    // Crash handling tests
    void testCrashHandlerSetup();
    void testCrashInfoGeneration();
    void testSystemInfoGathering();
    void testMemoryInfoGathering();
    void testBuildInfoGathering();

    // Stress and edge case tests
    void testManyErrorsReporting();
    void testConcurrentErrorReporting();
    void testErrorReportingDuringShutdown();
    void testInvalidErrorData();
    void testMemoryPressure();

private:
    ErrorHandler* m_errorHandler;
    QTemporaryDir* m_tempDir;
    
    void waitForSignal(QObject* sender, const char* signal, int timeout = 1000);
    bool containsErrorInHistory(const QString& title, const QString& message);
};

void TestErrorHandler::initTestCase()
{
    // Create temporary directory for test files
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    
    // Get error handler instance
    m_errorHandler = ErrorHandler::instance();
    QVERIFY(m_errorHandler != nullptr);
    
    // Initialize logger to reduce noise
    Logger* logger = Logger::instance();
    logger->setLogToConsole(false);
}

void TestErrorHandler::cleanupTestCase()
{
    delete m_tempDir;
    m_tempDir = nullptr;
}

void TestErrorHandler::init()
{
    // Reset error handler configuration before each test
    m_errorHandler->setShowUserNotifications(false); // Disable UI dialogs during tests
    m_errorHandler->setAutoReporting(false);
    m_errorHandler->setMaxErrorHistory(100);
    m_errorHandler->clearErrorHistory();
}

void TestErrorHandler::cleanup()
{
    // Clean up after each test
    m_errorHandler->clearErrorHistory();
}

void TestErrorHandler::testSingletonInstance()
{
    ErrorHandler* handler1 = ErrorHandler::instance();
    ErrorHandler* handler2 = ErrorHandler::instance();
    
    QVERIFY(handler1 != nullptr);
    QVERIFY(handler2 != nullptr);
    QCOMPARE(handler1, handler2); // Should be the same instance
    QCOMPARE(handler1, m_errorHandler); // Should be our test instance
}

void TestErrorHandler::testSingletonThreadSafety()
{
    QList<ErrorHandler*> instances;
    QMutex instancesMutex;
    
    // Create multiple threads that try to get the singleton instance
    QList<QThread*> threads;
    for (int i = 0; i < 10; ++i) {
        QThread* thread = QThread::create([&instances, &instancesMutex]() {
            ErrorHandler* instance = ErrorHandler::instance();
            QMutexLocker locker(&instancesMutex);
            instances.append(instance);
        });
        threads.append(thread);
        thread->start();
    }
    
    // Wait for all threads to complete
    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }
    
    // All instances should be the same
    QCOMPARE(instances.size(), 10);
    for (ErrorHandler* instance : instances) {
        QCOMPARE(instance, m_errorHandler);
    }
}

void TestErrorHandler::testBasicErrorReporting()
{
    QSignalSpy spy(m_errorHandler, &ErrorHandler::errorReported);
    
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "Test Error", "This is a test error message",
                               "Additional details", "TestLocation");
    
    QCOMPARE(spy.count(), 1);
    
    // Check error history
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 1);
    
    const ErrorInfo& error = history.first();
    QCOMPARE(error.severity, ErrorSeverity::Error);
    QCOMPARE(error.category, ErrorCategory::System);
    QCOMPARE(error.title, QString("Test Error"));
    QCOMPARE(error.message, QString("This is a test error message"));
    QCOMPARE(error.details, QString("Additional details"));
    QCOMPARE(error.location, QString("TestLocation"));
    QVERIFY(error.timestamp.isValid());
    QCOMPARE(error.userNotified, false);
}

void TestErrorHandler::testErrorSeverityLevels()
{
    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);
    QSignalSpy criticalSpy(m_errorHandler, &ErrorHandler::criticalErrorOccurred);
    
    // Test all severity levels
    m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System, "Info", "Info message");
    m_errorHandler->reportError(ErrorSeverity::Warning, ErrorCategory::System, "Warning", "Warning message");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "Error", "Error message");
    m_errorHandler->reportError(ErrorSeverity::Critical, ErrorCategory::System, "Critical", "Critical message");
    m_errorHandler->reportError(ErrorSeverity::Fatal, ErrorCategory::System, "Fatal", "Fatal message");
    
    QCOMPARE(errorSpy.count(), 5); // All errors should be reported
    QCOMPARE(criticalSpy.count(), 2); // Only Critical and Fatal should trigger critical signal
    
    // Verify severity levels in history
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 5);
    
    QCOMPARE(history[0].severity, ErrorSeverity::Info);
    QCOMPARE(history[1].severity, ErrorSeverity::Warning);
    QCOMPARE(history[2].severity, ErrorSeverity::Error);
    QCOMPARE(history[3].severity, ErrorSeverity::Critical);
    QCOMPARE(history[4].severity, ErrorSeverity::Fatal);
}

void TestErrorHandler::testErrorCategories()
{
    // Test all error categories
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "System", "System error");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::FileIO, "FileIO", "File error");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::PDF, "PDF", "PDF error");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::UI, "UI", "UI error");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::Network, "Network", "Network error");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::Memory, "Memory", "Memory error");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::Unknown, "Unknown", "Unknown error");
    
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 7);
    
    QCOMPARE(history[0].category, ErrorCategory::System);
    QCOMPARE(history[1].category, ErrorCategory::FileIO);
    QCOMPARE(history[2].category, ErrorCategory::PDF);
    QCOMPARE(history[3].category, ErrorCategory::UI);
    QCOMPARE(history[4].category, ErrorCategory::Network);
    QCOMPARE(history[5].category, ErrorCategory::Memory);
    QCOMPARE(history[6].category, ErrorCategory::Unknown);
}

void TestErrorHandler::testErrorSignalEmission()
{
    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);
    QSignalSpy criticalSpy(m_errorHandler, &ErrorHandler::criticalErrorOccurred);
    
    // Test normal error
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "Test", "Message");
    
    QCOMPARE(errorSpy.count(), 1);
    QCOMPARE(criticalSpy.count(), 0);
    
    QList<QVariant> errorArgs = errorSpy.takeFirst();
    ErrorInfo reportedError = errorArgs.at(0).value<ErrorInfo>();
    QCOMPARE(reportedError.title, QString("Test"));
    QCOMPARE(reportedError.message, QString("Message"));
    
    // Test critical error
    m_errorHandler->reportError(ErrorSeverity::Critical, ErrorCategory::System, "Critical Test", "Critical Message");
    
    QCOMPARE(errorSpy.count(), 1);
    QCOMPARE(criticalSpy.count(), 1);
    
    QList<QVariant> criticalArgs = criticalSpy.takeFirst();
    ErrorInfo criticalError = criticalArgs.at(0).value<ErrorInfo>();
    QCOMPARE(criticalError.title, QString("Critical Test"));
    QCOMPARE(criticalError.message, QString("Critical Message"));
}

void TestErrorHandler::testCriticalErrorHandling()
{
    QSignalSpy criticalSpy(m_errorHandler, &ErrorHandler::criticalErrorOccurred);
    
    // Test that critical and fatal errors trigger the critical signal
    m_errorHandler->reportError(ErrorSeverity::Critical, ErrorCategory::System, "Critical", "Critical error");
    m_errorHandler->reportError(ErrorSeverity::Fatal, ErrorCategory::System, "Fatal", "Fatal error");
    
    QCOMPARE(criticalSpy.count(), 2);
    
    // Verify both errors are in history
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 2);
    QCOMPARE(history[0].severity, ErrorSeverity::Critical);
    QCOMPARE(history[1].severity, ErrorSeverity::Fatal);
}

void TestErrorHandler::waitForSignal(QObject* sender, const char* signal, int timeout)
{
    QSignalSpy spy(sender, signal);
    QEventLoop loop;
    QTimer::singleShot(timeout, &loop, &QEventLoop::quit);
    QObject::connect(sender, signal, &loop, SLOT(quit()));
    loop.exec();
}

bool TestErrorHandler::containsErrorInHistory(const QString& title, const QString& message)
{
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    for (const ErrorInfo& error : history) {
        if (error.title == title && error.message == message) {
            return true;
        }
    }
    return false;
}

void TestErrorHandler::testErrorInfoStructure()
{
    m_errorHandler->reportError(ErrorSeverity::Warning, ErrorCategory::PDF,
                               "Structure Test", "Testing error info structure",
                               "Detailed information", "TestFunction");

    ErrorInfo lastError = m_errorHandler->getLastError();

    QCOMPARE(lastError.severity, ErrorSeverity::Warning);
    QCOMPARE(lastError.category, ErrorCategory::PDF);
    QCOMPARE(lastError.title, QString("Structure Test"));
    QCOMPARE(lastError.message, QString("Testing error info structure"));
    QCOMPARE(lastError.details, QString("Detailed information"));
    QCOMPARE(lastError.location, QString("TestFunction"));
    QVERIFY(lastError.timestamp.isValid());
    QCOMPARE(lastError.userNotified, false);
}

void TestErrorHandler::testErrorTimestamp()
{
    QDateTime beforeReport = QDateTime::currentDateTime();

    m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System,
                               "Timestamp Test", "Testing timestamp accuracy");

    QDateTime afterReport = QDateTime::currentDateTime();

    ErrorInfo lastError = m_errorHandler->getLastError();
    QVERIFY(lastError.timestamp >= beforeReport);
    QVERIFY(lastError.timestamp <= afterReport);
}

void TestErrorHandler::testErrorLocation()
{
    // Test with location
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "Location Test", "Message", "", "TestLocation");

    ErrorInfo errorWithLocation = m_errorHandler->getLastError();
    QCOMPARE(errorWithLocation.location, QString("TestLocation"));

    // Test without location
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "No Location Test", "Message");

    ErrorInfo errorWithoutLocation = m_errorHandler->getLastError();
    QVERIFY(errorWithoutLocation.location.isEmpty());
}

void TestErrorHandler::testErrorDetails()
{
    // Test with details
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "Details Test", "Message", "Detailed information");

    ErrorInfo errorWithDetails = m_errorHandler->getLastError();
    QCOMPARE(errorWithDetails.details, QString("Detailed information"));

    // Test without details
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "No Details Test", "Message");

    ErrorInfo errorWithoutDetails = m_errorHandler->getLastError();
    QVERIFY(errorWithoutDetails.details.isEmpty());
}

void TestErrorHandler::testUserNotificationConfiguration()
{
    // Test enabling user notifications
    m_errorHandler->setShowUserNotifications(true);
    QCOMPARE(m_errorHandler->getShowUserNotifications(), true);

    // Test disabling user notifications
    m_errorHandler->setShowUserNotifications(false);
    QCOMPARE(m_errorHandler->getShowUserNotifications(), false);
}

void TestErrorHandler::testAutoReportingConfiguration()
{
    // Test enabling auto reporting
    m_errorHandler->setAutoReporting(true);
    QCOMPARE(m_errorHandler->getAutoReporting(), true);

    // Test disabling auto reporting
    m_errorHandler->setAutoReporting(false);
    QCOMPARE(m_errorHandler->getAutoReporting(), false);
}

void TestErrorHandler::testMaxErrorHistoryConfiguration()
{
    // Test setting max error history
    m_errorHandler->setMaxErrorHistory(50);
    QCOMPARE(m_errorHandler->getMaxErrorHistory(), 50);

    m_errorHandler->setMaxErrorHistory(200);
    QCOMPARE(m_errorHandler->getMaxErrorHistory(), 200);
}

void TestErrorHandler::testErrorHistoryStorage()
{
    // Report multiple errors
    for (int i = 0; i < 5; ++i) {
        m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System,
                                   QString("Error %1").arg(i), QString("Message %1").arg(i));
    }

    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 5);

    // Verify order (should be chronological)
    for (int i = 0; i < 5; ++i) {
        QCOMPARE(history[i].title, QString("Error %1").arg(i));
        QCOMPARE(history[i].message, QString("Message %1").arg(i));
    }
}

void TestErrorHandler::testErrorHistoryTrimming()
{
    // Set small max history
    m_errorHandler->setMaxErrorHistory(3);

    // Report more errors than the limit
    for (int i = 0; i < 5; ++i) {
        m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System,
                                   QString("Error %1").arg(i), QString("Message %1").arg(i));
    }

    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 3);

    // Should contain the last 3 errors
    QCOMPARE(history[0].title, QString("Error 2"));
    QCOMPARE(history[1].title, QString("Error 3"));
    QCOMPARE(history[2].title, QString("Error 4"));
}

void TestErrorHandler::testErrorHistoryRetrieval()
{
    // Report some errors
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "Error 1", "Message 1");
    m_errorHandler->reportError(ErrorSeverity::Warning, ErrorCategory::PDF, "Error 2", "Message 2");
    m_errorHandler->reportError(ErrorSeverity::Critical, ErrorCategory::UI, "Error 3", "Message 3");

    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QCOMPARE(history.size(), 3);

    // Test getLastError
    ErrorInfo lastError = m_errorHandler->getLastError();
    QCOMPARE(lastError.title, QString("Error 3"));
    QCOMPARE(lastError.severity, ErrorSeverity::Critical);
}

void TestErrorHandler::testErrorHistoryClear()
{
    // Report some errors
    for (int i = 0; i < 3; ++i) {
        m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System,
                                   QString("Error %1").arg(i), QString("Message %1").arg(i));
    }

    QCOMPARE(m_errorHandler->getErrorHistory().size(), 3);

    // Clear history
    m_errorHandler->clearErrorHistory();
    QCOMPARE(m_errorHandler->getErrorHistory().size(), 0);
}

void TestErrorHandler::testErrorCountBySeverity()
{
    // Report errors of different severities
    m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System, "Info 1", "Message");
    m_errorHandler->reportError(ErrorSeverity::Info, ErrorCategory::System, "Info 2", "Message");
    m_errorHandler->reportError(ErrorSeverity::Warning, ErrorCategory::System, "Warning 1", "Message");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "Error 1", "Message");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "Error 2", "Message");
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "Error 3", "Message");
    m_errorHandler->reportError(ErrorSeverity::Critical, ErrorCategory::System, "Critical 1", "Message");

    // Test error counts
    QCOMPARE(m_errorHandler->getErrorCount(ErrorSeverity::Info), 2);
    QCOMPARE(m_errorHandler->getErrorCount(ErrorSeverity::Warning), 1);
    QCOMPARE(m_errorHandler->getErrorCount(ErrorSeverity::Error), 3);
    QCOMPARE(m_errorHandler->getErrorCount(ErrorSeverity::Critical), 1);
    QCOMPARE(m_errorHandler->getErrorCount(ErrorSeverity::Fatal), 0);
}

void TestErrorHandler::testExceptionReporting()
{
    QSignalSpy spy(m_errorHandler, &ErrorHandler::errorReported);

    TestException testException("Test exception message");
    m_errorHandler->reportException(testException, "TestLocation");

    QCOMPARE(spy.count(), 1);

    ErrorInfo lastError = m_errorHandler->getLastError();
    QCOMPARE(lastError.severity, ErrorSeverity::Error);
    QCOMPARE(lastError.category, ErrorCategory::System);
    QCOMPARE(lastError.title, QString("Exception Occurred"));
    QVERIFY(lastError.message.contains("Test exception message"));
    QCOMPARE(lastError.location, QString("TestLocation"));
}

void TestErrorHandler::testSystemErrorReporting()
{
    m_errorHandler->reportSystemError("file_open", 2, "TestLocation");

    ErrorInfo lastError = m_errorHandler->getLastError();
    QCOMPARE(lastError.severity, ErrorSeverity::Error);
    QCOMPARE(lastError.category, ErrorCategory::System);
    QCOMPARE(lastError.title, QString("System Error"));
    QVERIFY(lastError.message.contains("file_open"));
    QVERIFY(lastError.message.contains("2"));
    QCOMPARE(lastError.location, QString("TestLocation"));
}

void TestErrorHandler::testFileErrorReporting()
{
    QString testFilePath = m_tempDir->path() + "/test.txt";
    m_errorHandler->reportFileError(testFilePath, "read", "TestLocation");

    ErrorInfo lastError = m_errorHandler->getLastError();
    QCOMPARE(lastError.category, ErrorCategory::FileIO);
    QVERIFY(lastError.message.contains(testFilePath));
    QVERIFY(lastError.message.contains("read"));
}

void TestErrorHandler::testPdfErrorReporting()
{
    QString testPdfPath = m_tempDir->path() + "/test.pdf";
    m_errorHandler->reportPdfError(testPdfPath, "parse", "TestLocation");

    ErrorInfo lastError = m_errorHandler->getLastError();
    QCOMPARE(lastError.category, ErrorCategory::PDF);
    QVERIFY(lastError.message.contains(testPdfPath));
    QVERIFY(lastError.message.contains("parse"));
}

void TestErrorHandler::testAutoRecoveryDetection()
{
    // Test categories that can auto-recover
    QVERIFY(m_errorHandler->canAutoRecover(ErrorCategory::UI, "UI error"));
    QVERIFY(m_errorHandler->canAutoRecover(ErrorCategory::Memory, "Memory error"));

    // Test categories that cannot auto-recover
    QVERIFY(!m_errorHandler->canAutoRecover(ErrorCategory::System, "System error"));
    QVERIFY(!m_errorHandler->canAutoRecover(ErrorCategory::FileIO, "File error"));
    QVERIFY(!m_errorHandler->canAutoRecover(ErrorCategory::PDF, "PDF error"));
    QVERIFY(!m_errorHandler->canAutoRecover(ErrorCategory::Network, "Network error"));
}

void TestErrorHandler::testAutoRecoveryAttempt()
{
    QSignalSpy recoverySpy(m_errorHandler, &ErrorHandler::recoveryAttempted);

    // Enable auto reporting
    m_errorHandler->setAutoReporting(true);

    // Report a UI error that can be auto-recovered
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::UI,
                               "UI Error", "Test UI error for auto-recovery");

    // Should attempt recovery
    QCOMPARE(recoverySpy.count(), 1);

    QList<QVariant> recoveryArgs = recoverySpy.takeFirst();
    ErrorInfo recoveredError = recoveryArgs.at(0).value<ErrorInfo>();
    bool recoverySuccess = recoveryArgs.at(1).toBool();

    QCOMPARE(recoveredError.category, ErrorCategory::UI);
    QVERIFY(recoverySuccess); // UI recovery should succeed
}

void TestErrorHandler::testAutoRecoverySignals()
{
    QSignalSpy recoverySpy(m_errorHandler, &ErrorHandler::recoveryAttempted);

    m_errorHandler->setAutoReporting(true);

    // Test UI recovery
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::UI, "UI Error", "Message");
    QCOMPARE(recoverySpy.count(), 1);

    // Test Memory recovery
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::Memory, "Memory Error", "Message");
    QCOMPARE(recoverySpy.count(), 2);

    // Test non-recoverable error
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System, "System Error", "Message");
    QCOMPARE(recoverySpy.count(), 2); // Should not increase
}

void TestErrorHandler::testUserNotificationEnabled()
{
    m_errorHandler->setShowUserNotifications(true);

    // This test mainly verifies that enabling notifications doesn't crash
    // In a real GUI environment, you would test that dialogs are shown
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "Notification Test", "Test notification");

    QVERIFY(true); // If we get here, no crash occurred
}

void TestErrorHandler::testUserNotificationDisabled()
{
    m_errorHandler->setShowUserNotifications(false);

    // Report an error - should not show notification
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "No Notification Test", "Test no notification");

    // Verify error is still recorded
    QVERIFY(containsErrorInHistory("No Notification Test", "Test no notification"));
}

void TestErrorHandler::testNotificationDelay()
{
    // This test verifies that the notification system uses a timer
    // The actual delay testing would require more complex timing verification
    m_errorHandler->setShowUserNotifications(true);

    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);

    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "Delay Test", "Test notification delay");

    // Error should be reported immediately
    QCOMPARE(errorSpy.count(), 1);

    // Notification would be shown after delay (tested indirectly)
    QVERIFY(true);
}

void TestErrorHandler::testQtMessageHandling()
{
    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);

    // Simulate Qt messages of different types
    QMessageLogContext context;
    context.file = "test.cpp";
    context.line = 123;
    context.function = "testFunction";

    // Test debug message
    m_errorHandler->handleQtMessage(QtDebugMsg, context, "Debug message");

    // Test warning message
    m_errorHandler->handleQtMessage(QtWarningMsg, context, "Warning message");

    // Test critical message
    m_errorHandler->handleQtMessage(QtCriticalMsg, context, "Critical message");

    // Test fatal message
    m_errorHandler->handleQtMessage(QtFatalMsg, context, "Fatal message");

    // Should have generated error reports for warning, critical, and fatal
    QVERIFY(errorSpy.count() >= 3);
}

void TestErrorHandler::testQtDebugMessages()
{
    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);

    QMessageLogContext context;
    m_errorHandler->handleQtMessage(QtDebugMsg, context, "Debug message");

    // Debug messages typically don't generate error reports
    // This depends on the implementation
    QVERIFY(true); // Test passes if no crash occurs
}

void TestErrorHandler::testQtWarningMessages()
{
    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);

    QMessageLogContext context;
    m_errorHandler->handleQtMessage(QtWarningMsg, context, "Warning message");

    // Should generate an error report
    QVERIFY(errorSpy.count() >= 1);

    if (errorSpy.count() > 0) {
        ErrorInfo lastError = m_errorHandler->getLastError();
        QVERIFY(lastError.message.contains("Warning message"));
    }
}

void TestErrorHandler::testQtCriticalMessages()
{
    QSignalSpy errorSpy(m_errorHandler, &ErrorHandler::errorReported);
    QSignalSpy criticalSpy(m_errorHandler, &ErrorHandler::criticalErrorOccurred);

    QMessageLogContext context;
    m_errorHandler->handleQtMessage(QtCriticalMsg, context, "Critical message");

    // Should generate error reports and possibly critical signals
    QVERIFY(errorSpy.count() >= 1);

    if (errorSpy.count() > 0) {
        ErrorInfo lastError = m_errorHandler->getLastError();
        QVERIFY(lastError.message.contains("Critical message"));
    }
}

void TestErrorHandler::testRecoverySuggestions()
{
    // Test recovery suggestions for different categories
    QString uiSuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::UI, "UI error");
    QVERIFY(!uiSuggestion.isEmpty());

    QString fileSuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::FileIO, "File not found");
    QVERIFY(!fileSuggestion.isEmpty());

    QString pdfSuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::PDF, "Invalid PDF");
    QVERIFY(!pdfSuggestion.isEmpty());

    QString memorySuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::Memory, "Out of memory");
    QVERIFY(!memorySuggestion.isEmpty());
}

void TestErrorHandler::testRecoverySuggestionsByCategory()
{
    // Test that different categories provide different suggestions
    QString uiSuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::UI, "error");
    QString fileSuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::FileIO, "error");
    QString pdfSuggestion = m_errorHandler->getRecoverySuggestion(ErrorCategory::PDF, "error");

    // Suggestions should be different for different categories
    QVERIFY(uiSuggestion != fileSuggestion);
    QVERIFY(fileSuggestion != pdfSuggestion);
    QVERIFY(uiSuggestion != pdfSuggestion);
}

void TestErrorHandler::testCrashHandlerSetup()
{
    // Test that crash handler can be set up without crashing
    m_errorHandler->setupCrashHandler();

    // If we get here, setup was successful
    QVERIFY(true);
}

void TestErrorHandler::testCrashInfoGeneration()
{
    // Test system info gathering
    QString systemInfo = m_errorHandler->getSystemInfo();
    QVERIFY(!systemInfo.isEmpty());
    QVERIFY(systemInfo.contains("OS:") || systemInfo.contains("System"));

    QString memoryInfo = m_errorHandler->getMemoryInfo();
    QVERIFY(!memoryInfo.isEmpty());

    QString buildInfo = m_errorHandler->getBuildInfo();
    QVERIFY(!buildInfo.isEmpty());
}

void TestErrorHandler::testSystemInfoGathering()
{
    QString systemInfo = m_errorHandler->getSystemInfo();

    QVERIFY(!systemInfo.isEmpty());
    // Should contain basic system information
    QVERIFY(systemInfo.length() > 10);
}

void TestErrorHandler::testMemoryInfoGathering()
{
    QString memoryInfo = m_errorHandler->getMemoryInfo();

    QVERIFY(!memoryInfo.isEmpty());
    // Should contain memory-related information
    QVERIFY(memoryInfo.length() > 5);
}

void TestErrorHandler::testBuildInfoGathering()
{
    QString buildInfo = m_errorHandler->getBuildInfo();

    QVERIFY(!buildInfo.isEmpty());
    // Should contain build-related information
    QVERIFY(buildInfo.length() > 5);
}

void TestErrorHandler::testManyErrorsReporting()
{
    // Test reporting many errors
    const int errorCount = 1000;

    for (int i = 0; i < errorCount; ++i) {
        ErrorSeverity severity = static_cast<ErrorSeverity>(i % 5);
        ErrorCategory category = static_cast<ErrorCategory>(i % 7);

        m_errorHandler->reportError(severity, category,
                                   QString("Error %1").arg(i),
                                   QString("Message %1").arg(i));
    }

    // Should handle many errors without crashing
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();

    // History should be limited by max history setting
    QVERIFY(history.size() <= m_errorHandler->getMaxErrorHistory());

    // If we get here, no crash occurred
    QVERIFY(true);
}

void TestErrorHandler::testConcurrentErrorReporting()
{
    QList<QThread*> threads;

    // Create multiple threads that report errors simultaneously
    for (int i = 0; i < 10; ++i) {
        QThread* thread = QThread::create([this, i]() {
            for (int j = 0; j < 50; ++j) {
                m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                                           QString("Thread %1 Error %2").arg(i).arg(j),
                                           QString("Concurrent error from thread %1").arg(i));
            }
        });
        threads.append(thread);
        thread->start();
    }

    // Wait for all threads to complete
    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }

    // Should handle concurrent access without crashing
    QList<ErrorInfo> history = m_errorHandler->getErrorHistory();
    QVERIFY(history.size() > 0);

    // If we get here, thread safety is working
    QVERIFY(true);
}

void TestErrorHandler::testErrorReportingDuringShutdown()
{
    // This test simulates error reporting during application shutdown
    // In a real scenario, this would be more complex

    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "Shutdown Error", "Error during shutdown");

    // Should handle gracefully
    QVERIFY(containsErrorInHistory("Shutdown Error", "Error during shutdown"));
}

void TestErrorHandler::testInvalidErrorData()
{
    // Test with empty strings
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               "", "", "", "");

    ErrorInfo lastError = m_errorHandler->getLastError();
    QCOMPARE(lastError.title, QString(""));
    QCOMPARE(lastError.message, QString(""));
    QCOMPARE(lastError.details, QString(""));
    QCOMPARE(lastError.location, QString(""));

    // Test with very long strings
    QString longString(10000, 'X');
    m_errorHandler->reportError(ErrorSeverity::Error, ErrorCategory::System,
                               longString, longString, longString, longString);

    ErrorInfo longError = m_errorHandler->getLastError();
    QCOMPARE(longError.title, longString);
    QCOMPARE(longError.message, longString);

    // Should handle without crashing
    QVERIFY(true);
}

void TestErrorHandler::testMemoryPressure()
{
    // Test error handling under memory pressure
    // This is a simplified test - real memory pressure testing is complex

    QList<QString> largeStrings;

    // Create some memory pressure
    for (int i = 0; i < 100; ++i) {
        largeStrings.append(QString(1000, 'M')); // 1KB strings

        // Report errors during memory pressure
        if (i % 10 == 0) {
            m_errorHandler->reportError(ErrorSeverity::Warning, ErrorCategory::Memory,
                                       QString("Memory Pressure %1").arg(i),
                                       "Error during memory pressure");
        }
    }

    // Should handle memory pressure gracefully
    QVERIFY(m_errorHandler->getErrorHistory().size() > 0);

    // Clean up
    largeStrings.clear();

    // If we get here, memory handling is working
    QVERIFY(true);
}

QTEST_MAIN(TestErrorHandler)
#include "test_error_handler.moc"
