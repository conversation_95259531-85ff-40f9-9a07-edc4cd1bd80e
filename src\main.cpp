// main.cpp
#include "MainWindow.h"
#include "ElaIntegration.h"
#include "Logger.h"
#include "ErrorHandler.h"
#include <QApplication>
#include <QScreen>
#include <QStyleHints>
#include <QTimer>
#include <QTimer>

int main(int argc, char *argv[])
{
    // Enable high-DPI scaling for better display on high-resolution monitors
    QApplication::setHighDpiScaleFactorRoundingPolicy(Qt::HighDpiScaleFactorRoundingPolicy::PassThrough);

    QApplication a(argc, argv);

    // Set application properties
    a.setApplicationName("Optimized PDF Viewer");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("PDF Viewer");

    // Initialize logging and error handling
    Logger* logger = Logger::instance();
    logger->setLogLevel(LogLevel::Info);
    logger->info("Application starting up", "Main");

    // Temporarily disable <PERSON><PERSON>r<PERSON><PERSON><PERSON> for debugging
    /*
    ErrorHandler* errorHandler = ErrorHandler::instance();
    errorHandler->setShowUserNotifications(true);
    errorHandler->setupCrashHandler();
    */
    logger->info("ErrorHandler initialization skipped for debugging", "Main");

    // Initialize ElaWidgetTools if available
    logger->info("Initializing ElaWidgetTools", "Main");
    try {
        if (ElaIntegration::isElaWidgetsAvailable()) {
            logger->info("ElaWidgetTools available, initializing...", "Main");
            ElaIntegration::initializeElaApplication();
            logger->info("ElaWidgetTools application initialized", "Main");
            
            ElaIntegration::applyElaTheme();
            logger->info("ElaWidgetTools theme applied", "Main");
        } else {
            logger->info("ElaWidgetTools not available, using fallback", "Main");
        }
    } catch (const std::exception& e) {
        logger->warning(QString("ElaWidgetTools initialization failed: %1").arg(e.what()), "Main");
    } catch (...) {
        logger->warning("ElaWidgetTools initialization failed with unknown error", "Main");
    }

    // Configure application for better rendering (Qt6 handles high-DPI pixmaps automatically)

    // Set up a timer to detect potential deadlocks
    QTimer* deadlockTimer = new QTimer();
    deadlockTimer->setSingleShot(true);
    deadlockTimer->setInterval(30000); // 30 second timeout
    
    bool mainWindowCreated = false;
    QObject::connect(deadlockTimer, &QTimer::timeout, [&]() {
        if (!mainWindowCreated) {
            logger->error("Main window creation timeout - potential deadlock detected", "Main");
            qApp->quit();
        }
    });
    
    deadlockTimer->start();

    try {
        logger->info("Creating main window", "Main");
        MainWindow w;
        mainWindowCreated = true;
        deadlockTimer->stop();
        logger->info("Main window created successfully", "Main");
        
        logger->info("Showing main window", "Main");

        // Skip moveToCenter() for QMainWindow testing
        logger->info("Skipping moveToCenter() for QMainWindow testing", "Main");

        logger->info("About to call w.show() directly (QMainWindow)", "Main");
        try {
            w.show();
            logger->info("w.show() call completed successfully", "Main");
        } catch (const std::exception& e) {
            logger->error(QString("w.show() failed: %1").arg(e.what()), "Main");
            return -1;
        } catch (...) {
            logger->error("w.show() failed with unknown error", "Main");
            return -1;
        }

        logger->info("Starting event loop", "Main");
        int result = a.exec();

        logger->info("Application shutting down normally", "Main");
        return result;

    } catch (const std::exception& e) {
        logger->error(QString("main: %1").arg(e.what()), "Main");
        return -1;
    } catch (...) {
        logger->error("Unknown exception occurred during startup", "Main");
        return -1;
    }
}
