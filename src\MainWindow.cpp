// MainWindow.cpp
#include "MainWindow.h"
#include "ElaIntegration.h"
#include "SettingsDialog.h"
#include "AnnotationToolbar.h"
#include "AnnotationManager.h"
#include "AnnotationSearchDialog.h"
#include "AnnotationClipboard.h"
#include "DocumentInfoDialog.h"
#include "SearchResultsPanel.h"
#include "PrintPreviewDialog.h"
#include "BookmarkDialog.h"
#include "WelcomeScreen.h"
#include "DocumentationViewer.h"
#include "Logger.h"
#include "ErrorHandler.h"
#include "DesignSystem.h"
#include "StyleHelper.h"

#include <QFileDialog>
#include <QScrollArea>
#include <QAction>
#include <QKeySequence>
#include <QFileInfo>
#include <QDir>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QDesktopServices>
#include <QResizeEvent>
#include <QTimer>
#include <QListWidgetItem>
#include <QTreeWidgetItem>
#include <QSplitter>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QSettings>
#include <QStandardPaths>
#include <QInputDialog>
#include <QPrinter>
#include <QPrintDialog>
#include <QPainter>
#include <QFileDialog>
#include <QProgressDialog>
#include <QApplication>
#include <QScreen>
#include <QKeyEvent>
#include <QMessageBox>
#include <QComboBox>
#include <QSlider>
#include <QMouseEvent>
#include <QInputDialog>
#include <QTabWidget>
#include <QProcess>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), m_welcomeScreen(nullptr), m_settings(nullptr)
{
    Logger* logger = Logger::instance();
    logger->info("MainWindow constructor started", "MainWindow");
    
    // Initialize settings
    logger->info("Initializing settings", "MainWindow");
    m_settings = new QSettings(this);
    logger->info("Settings initialized", "MainWindow");

    // Initialize search history
    logger->info("Initializing search history", "MainWindow");
    m_searchHistory = new SearchHistory(this);
    logger->info("Search history initialized", "MainWindow");

    // Initialize UI update timer for throttling
    logger->info("Initializing UI update timer", "MainWindow");
    m_uiUpdateTimer = new QTimer(this);
    m_uiUpdateTimer->setSingleShot(true);
    m_uiUpdateTimer->setInterval(50); // 50ms throttling for smooth UI updates
    connect(m_uiUpdateTimer, &QTimer::timeout, this, &MainWindow::performScheduledUiUpdate);
    logger->info("UI update timer initialized", "MainWindow");

    // Connect to crash handling system - temporarily disabled for debugging
    logger->info("Skipping crash handling system connection for debugging", "MainWindow");
    // connectCrashHandling();
    logger->info("Crash handling system connection skipped", "MainWindow");

    // Initialize QMainWindow (simplified for testing)
    logger->info("Initializing QMainWindow", "MainWindow");
    try {
        // Basic window setup for QMainWindow
        setWindowIcon(QIcon(":/icons/app.png"));
        resize(1400, 900);
        setWindowTitle(tr("Optimized PDF Viewer"));
        logger->info("QMainWindow initialized successfully", "MainWindow");
    } catch (const std::exception& e) {
        logger->error(QString("QMainWindow initialization failed: %1").arg(e.what()), "MainWindow");
        throw;
    } catch (...) {
        logger->error("QMainWindow initialization failed with unknown error", "MainWindow");
        throw;
    }

    // Create UI with modern navigation - gradually re-enabling components
    logger->info("Creating UI (gradually re-enabling components)", "MainWindow");
    try {
        // Create the tab widget
        m_tabWidget = new ElaTab(this);
        m_tabWidget->setTabsClosable(true);
        m_tabWidget->setMovable(true);
        m_tabWidget->setDocumentMode(true);
        logger->info("Tab widget created", "MainWindow");

        // Connect tab widget signals
        connect(m_tabWidget, &QTabWidget::tabCloseRequested, this, &MainWindow::closeTab);
        connect(m_tabWidget, &QTabWidget::currentChanged, this, &MainWindow::onTabChanged);
        logger->info("Tab widget signals connected", "MainWindow");

        // Add tab widget as central widget to QMainWindow
        setCentralWidget(m_tabWidget);
        logger->info("Central widget added", "MainWindow");

        // Create welcome screen (needed for navigation)
        logger->info("Creating welcome screen", "MainWindow");
        createWelcomeScreen();
        logger->info("Welcome screen created", "MainWindow");

        // Create actions (might be needed for ElaMainWindow)
        logger->info("Creating actions", "MainWindow");
        createActions();
        logger->info("Actions created", "MainWindow");

        // Create essential panels (might be needed for ElaMainWindow)
        logger->info("Creating thumbnails panel", "MainWindow");
        createThumbnailsPanel();
        logger->info("Thumbnails panel created", "MainWindow");

        logger->info("Creating outline panel", "MainWindow");
        createOutlinePanel();
        logger->info("Outline panel created", "MainWindow");

        // Create status bar (might be essential for ElaMainWindow)
        logger->info("Creating status bar", "MainWindow");
        createStatusBar();
        logger->info("Status bar created", "MainWindow");

        logger->info("UI created successfully (with welcome screen, actions, panels, and status bar)", "MainWindow");
    } catch (const std::exception& e) {
        logger->error(QString("createUi failed: %1").arg(e.what()), "MainWindow");
        throw;
    } catch (...) {
        logger->error("createUi failed with unknown error", "MainWindow");
        throw;
    }

    // Skip navigation initialization for QMainWindow testing
    logger->info("Skipping navigation initialization for QMainWindow testing", "MainWindow");

    // Re-enabling content initialization
    logger->info("Initializing content", "MainWindow");
    try {
        initContent();
        logger->info("Content initialized successfully", "MainWindow");
    } catch (const std::exception& e) {
        logger->error(QString("initContent failed: %1").arg(e.what()), "MainWindow");
        throw;
    } catch (...) {
        logger->error("initContent failed with unknown error", "MainWindow");
        throw;
    }

    // Enable drag and drop
    logger->info("Enabling drag and drop", "MainWindow");
    setAcceptDrops(true);
    logger->info("Drag and drop enabled", "MainWindow");

    logger->info("Updating UI state", "MainWindow");
    updateUiState();
    logger->info("UI state updated", "MainWindow");
    
    logger->info("Loading settings", "MainWindow");
    loadSettings();
    logger->info("Settings loaded", "MainWindow");

    // Track application startup - temporarily disabled for debugging
    logger->info("Skipping user action tracking for debugging", "MainWindow");
    // trackUserAction("Application started successfully");
    logger->info("MainWindow constructor completed successfully", "MainWindow");
}

MainWindow::~MainWindow()
{
    saveSettings();
}

void MainWindow::createUi()
{
    Logger* logger = Logger::instance();
    logger->info("Creating tab widget", "MainWindow");
    
    // Create the tab widget
    m_tabWidget = new ElaTab(this);
    m_tabWidget->setTabsClosable(true);
    m_tabWidget->setMovable(true);
    m_tabWidget->setDocumentMode(true);
    logger->info("Tab widget created", "MainWindow");

    logger->info("Connecting tab widget signals", "MainWindow");
    connect(m_tabWidget, &QTabWidget::tabCloseRequested, this, &MainWindow::closeTab);
    connect(m_tabWidget, &QTabWidget::currentChanged, this, &MainWindow::onTabChanged);
    logger->info("Tab widget signals connected", "MainWindow");

    // Add tab widget as central widget to ElaMainWindow
    logger->info("Adding central widget", "MainWindow");
    addCentralWidget(m_tabWidget);
    logger->info("Central widget added", "MainWindow");

    // Create actions and panels
    logger->info("Creating actions", "MainWindow");
    createActions();
    logger->info("Actions created", "MainWindow");
    
    logger->info("Creating thumbnails panel", "MainWindow");
    createThumbnailsPanel();
    logger->info("Thumbnails panel created", "MainWindow");
    
    logger->info("Creating outline panel", "MainWindow");
    createOutlinePanel();
    logger->info("Outline panel created", "MainWindow");
    
    logger->info("Creating search results panel", "MainWindow");
    createSearchResultsPanel();
    logger->info("Search results panel created", "MainWindow");
    
    logger->info("Creating search bar", "MainWindow");
    createSearchBar();
    logger->info("Search bar created", "MainWindow");
    
    logger->info("Creating annotation toolbar", "MainWindow");
    createAnnotationToolbar();
    logger->info("Annotation toolbar created", "MainWindow");
    
    logger->info("Creating welcome screen", "MainWindow");
    createWelcomeScreen();
    logger->info("Welcome screen created", "MainWindow");

    // Create initial tab
    logger->info("Creating new tab", "MainWindow");
    createNewTab();
    logger->info("New tab created", "MainWindow");

    // Set window properties
    logger->info("Setting window properties", "MainWindow");
    setWindowTitle(tr("Optimized PDF Viewer"));
    resize(800, 600);
    logger->info("Window properties set", "MainWindow");

    // Restore session after UI is fully created
    logger->info("Scheduling session restore", "MainWindow");
    QTimer::singleShot(100, this, &MainWindow::restoreSession);
    logger->info("Session restore scheduled", "MainWindow");
}

void MainWindow::createActions()
{
    // File actions
    m_openAction = new QAction(tr("&Open..."), this);
    m_openAction->setShortcut(QKeySequence::Open);
    m_openAction->setStatusTip(tr("Open a PDF file"));
    connect(m_openAction, &QAction::triggered, this, &MainWindow::openPdf);

    // Navigation actions
    m_nextPageAction = new QAction(tr("&Next Page"), this);
    m_nextPageAction->setShortcut(QKeySequence::MoveToNextPage);
    m_nextPageAction->setStatusTip(tr("Go to next page"));
    connect(m_nextPageAction, &QAction::triggered, this, &MainWindow::nextPage);

    m_prevPageAction = new QAction(tr("&Previous Page"), this);
    m_prevPageAction->setShortcut(QKeySequence::MoveToPreviousPage);
    m_prevPageAction->setStatusTip(tr("Go to previous page"));
    connect(m_prevPageAction, &QAction::triggered, this, &MainWindow::previousPage);

    // Zoom actions with improved tooltips
    m_zoomInAction = new QAction(tr("Zoom &In"), this);
    m_zoomInAction->setShortcut(QKeySequence::ZoomIn);
    m_zoomInAction->setStatusTip(tr("Zoom in"));
    m_zoomInAction->setToolTip(tr("Zoom in (Ctrl++)"));
    connect(m_zoomInAction, &QAction::triggered, this, &MainWindow::zoomIn);

    m_zoomOutAction = new QAction(tr("Zoom &Out"), this);
    m_zoomOutAction->setShortcut(QKeySequence::ZoomOut);
    m_zoomOutAction->setStatusTip(tr("Zoom out"));
    m_zoomOutAction->setToolTip(tr("Zoom out (Ctrl+-)"));
    connect(m_zoomOutAction, &QAction::triggered, this, &MainWindow::zoomOut);

    // Fit-to-window actions with improved tooltips
    m_fitToWindowAction = new QAction(tr("Fit to &Window"), this);
    m_fitToWindowAction->setShortcut(QKeySequence(Qt::Key_Escape));
    m_fitToWindowAction->setStatusTip(tr("Fit page to window"));
    m_fitToWindowAction->setToolTip(tr("Fit page to window (Esc)"));
    connect(m_fitToWindowAction, &QAction::triggered, this, &MainWindow::fitToWindow);

    m_fitToWidthAction = new QAction(tr("Fit to Widt&h"), this);
    m_fitToWidthAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_0));
    m_fitToWidthAction->setStatusTip(tr("Fit page width to window"));
    m_fitToWidthAction->setToolTip(tr("Fit page width to window (Ctrl+0)"));
    connect(m_fitToWidthAction, &QAction::triggered, this, &MainWindow::fitToWidth);

    // Additional navigation shortcuts with improved tooltips
    m_firstPageAction = new QAction(tr("&First Page"), this);
    m_firstPageAction->setShortcut(QKeySequence::MoveToStartOfDocument);
    m_firstPageAction->setStatusTip(tr("Go to first page"));
    m_firstPageAction->setToolTip(tr("First page (Ctrl+Home)"));
    connect(m_firstPageAction, &QAction::triggered, this, &MainWindow::firstPage);

    m_lastPageAction = new QAction(tr("&Last Page"), this);
    m_lastPageAction->setShortcut(QKeySequence::MoveToEndOfDocument);
    m_lastPageAction->setStatusTip(tr("Go to last page"));
    m_lastPageAction->setToolTip(tr("Last page (Ctrl+End)"));
    connect(m_lastPageAction, &QAction::triggered, this, &MainWindow::lastPage);

    // Quick zoom shortcuts
    m_zoom100Action = new QAction(tr("&Actual Size"), this);
    m_zoom100Action->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_1));
    m_zoom100Action->setStatusTip(tr("Zoom to 100%"));
    m_zoom100Action->setToolTip(tr("Actual size (Ctrl+1)"));
    connect(m_zoom100Action, &QAction::triggered, this, &MainWindow::zoom100);

    // Additional keyboard shortcuts for better navigation
    QAction* zoomInAltAction = new QAction(this);
    zoomInAltAction->setShortcut(QKeySequence(Qt::Key_Plus));
    connect(zoomInAltAction, &QAction::triggered, this, &MainWindow::zoomIn);
    addAction(zoomInAltAction);

    QAction* zoomOutAltAction = new QAction(this);
    zoomOutAltAction->setShortcut(QKeySequence(Qt::Key_Minus));
    connect(zoomOutAltAction, &QAction::triggered, this, &MainWindow::zoomOut);
    addAction(zoomOutAltAction);

    // Enhanced navigation shortcuts
    // Space bar for next page, Shift+Space for previous page
    QAction* spaceNextAction = new QAction(this);
    spaceNextAction->setShortcut(QKeySequence(Qt::Key_Space));
    connect(spaceNextAction, &QAction::triggered, this, &MainWindow::nextPage);
    addAction(spaceNextAction);

    QAction* shiftSpacePrevAction = new QAction(this);
    shiftSpacePrevAction->setShortcut(QKeySequence(Qt::SHIFT | Qt::Key_Space));
    connect(shiftSpacePrevAction, &QAction::triggered, this, &MainWindow::previousPage);
    addAction(shiftSpacePrevAction);

    // Arrow key navigation
    QAction* leftArrowAction = new QAction(this);
    leftArrowAction->setShortcut(QKeySequence(Qt::Key_Left));
    connect(leftArrowAction, &QAction::triggered, this, &MainWindow::previousPage);
    addAction(leftArrowAction);

    QAction* rightArrowAction = new QAction(this);
    rightArrowAction->setShortcut(QKeySequence(Qt::Key_Right));
    connect(rightArrowAction, &QAction::triggered, this, &MainWindow::nextPage);
    addAction(rightArrowAction);

    QAction* upArrowAction = new QAction(this);
    upArrowAction->setShortcut(QKeySequence(Qt::Key_Up));
    connect(upArrowAction, &QAction::triggered, this, &MainWindow::previousPage);
    addAction(upArrowAction);

    QAction* downArrowAction = new QAction(this);
    downArrowAction->setShortcut(QKeySequence(Qt::Key_Down));
    connect(downArrowAction, &QAction::triggered, this, &MainWindow::nextPage);
    addAction(downArrowAction);

    // Page Up/Down navigation
    QAction* pageUpAction = new QAction(this);
    pageUpAction->setShortcut(QKeySequence(Qt::Key_PageUp));
    connect(pageUpAction, &QAction::triggered, this, &MainWindow::previousPage);
    addAction(pageUpAction);

    QAction* pageDownAction = new QAction(this);
    pageDownAction->setShortcut(QKeySequence(Qt::Key_PageDown));
    connect(pageDownAction, &QAction::triggered, this, &MainWindow::nextPage);
    addAction(pageDownAction);

    // Additional zoom shortcuts
    QAction* equalZoomInAction = new QAction(this);
    equalZoomInAction->setShortcut(QKeySequence(Qt::Key_Equal));
    connect(equalZoomInAction, &QAction::triggered, this, &MainWindow::zoomIn);
    addAction(equalZoomInAction);

    // Numpad shortcuts
    QAction* numPadPlusAction = new QAction(this);
    numPadPlusAction->setShortcut(QKeySequence(Qt::Key_Plus));
    connect(numPadPlusAction, &QAction::triggered, this, &MainWindow::zoomIn);
    addAction(numPadPlusAction);

    QAction* numPadMinusAction = new QAction(this);
    numPadMinusAction->setShortcut(QKeySequence(Qt::Key_Minus));
    connect(numPadMinusAction, &QAction::triggered, this, &MainWindow::zoomOut);
    addAction(numPadMinusAction);

    // View actions
    m_toggleThumbnailsAction = new QAction(tr("&Thumbnails"), this);
    m_toggleThumbnailsAction->setShortcut(QKeySequence(Qt::Key_F9));
    m_toggleThumbnailsAction->setStatusTip(tr("Toggle thumbnails panel"));
    m_toggleThumbnailsAction->setCheckable(true);
    m_toggleThumbnailsAction->setChecked(true);
    connect(m_toggleThumbnailsAction, &QAction::triggered, this, &MainWindow::toggleThumbnails);

    m_toggleOutlineAction = new QAction(tr("&Outline"), this);
    m_toggleOutlineAction->setShortcut(QKeySequence(Qt::Key_F8));
    m_toggleOutlineAction->setStatusTip(tr("Toggle document outline panel"));
    m_toggleOutlineAction->setCheckable(true);
    m_toggleOutlineAction->setChecked(true);
    connect(m_toggleOutlineAction, &QAction::triggered, this, &MainWindow::toggleOutline);

    m_toggleSearchResultsAction = new QAction(tr("&Search Results"), this);
    m_toggleSearchResultsAction->setShortcut(QKeySequence(Qt::Key_F3));
    m_toggleSearchResultsAction->setStatusTip(tr("Toggle search results panel"));
    m_toggleSearchResultsAction->setToolTip(tr("Search results panel (F3)"));
    m_toggleSearchResultsAction->setCheckable(true);
    m_toggleSearchResultsAction->setChecked(false);
    connect(m_toggleSearchResultsAction, &QAction::triggered, this, &MainWindow::toggleSearchResults);

    // Fullscreen action
    m_fullScreenAction = new QAction(tr("&Full Screen"), this);
    m_fullScreenAction->setShortcut(QKeySequence(Qt::Key_F11));
    m_fullScreenAction->setStatusTip(tr("Enter full screen presentation mode"));
    m_fullScreenAction->setCheckable(true);
    connect(m_fullScreenAction, &QAction::triggered, this, &MainWindow::toggleFullScreen);

    // Advanced zoom actions
    m_selectionZoomAction = new QAction(tr("Selection &Zoom"), this);
    m_selectionZoomAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_Z));
    m_selectionZoomAction->setStatusTip(tr("Click and drag to zoom to selection"));
    m_selectionZoomAction->setCheckable(true);
    connect(m_selectionZoomAction, &QAction::triggered, this, &MainWindow::toggleSelectionZoom);

    m_magnifierAction = new QAction(tr("&Magnifier"), this);
    m_magnifierAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_M));
    m_magnifierAction->setStatusTip(tr("Magnifier tool for detailed viewing"));
    m_magnifierAction->setCheckable(true);
    connect(m_magnifierAction, &QAction::triggered, this, &MainWindow::toggleMagnifier);

    m_customZoomAction = new QAction(tr("&Custom Zoom..."), this);
    m_customZoomAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_Z));
    m_customZoomAction->setStatusTip(tr("Set custom zoom level"));
    connect(m_customZoomAction, &QAction::triggered, this, &MainWindow::showCustomZoomDialog);

    // Tab actions
    m_newTabAction = new QAction(tr("&New Tab"), this);
    m_newTabAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_T));
    m_newTabAction->setStatusTip(tr("Open a new tab"));
    connect(m_newTabAction, &QAction::triggered, this, &MainWindow::newTab);

    m_closeTabAction = new QAction(tr("&Close Tab"), this);
    m_closeTabAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_W));
    m_closeTabAction->setStatusTip(tr("Close current tab"));
    connect(m_closeTabAction, &QAction::triggered, this, &MainWindow::closeCurrentTab);

    // Search action
    m_searchAction = new QAction(tr("&Find..."), this);
    m_searchAction->setShortcut(QKeySequence::Find);
    m_searchAction->setStatusTip(tr("Search for text in the document"));
    connect(m_searchAction, &QAction::triggered, this, &MainWindow::showSearchBar);

    // Bookmark actions
    m_addBookmarkAction = new QAction(tr("Add &Bookmark..."), this);
    m_addBookmarkAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_B));
    m_addBookmarkAction->setStatusTip(tr("Add bookmark for current page"));
    connect(m_addBookmarkAction, &QAction::triggered, this, &MainWindow::addBookmark);

    m_goToBookmarkAction = new QAction(tr("&Manage Bookmarks..."), this);
    m_goToBookmarkAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_B));
    m_goToBookmarkAction->setStatusTip(tr("Manage bookmarks"));
    connect(m_goToBookmarkAction, &QAction::triggered, this, &MainWindow::goToBookmark);

    // Rotation actions
    m_rotateClockwiseAction = new QAction(tr("Rotate &Clockwise"), this);
    m_rotateClockwiseAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_R));
    m_rotateClockwiseAction->setStatusTip(tr("Rotate page 90° clockwise"));
    m_rotateClockwiseAction->setToolTip(tr("Rotate clockwise (Ctrl+R)"));
    connect(m_rotateClockwiseAction, &QAction::triggered, this, &MainWindow::rotateClockwise);

    m_rotateCounterClockwiseAction = new QAction(tr("Rotate &Counter-clockwise"), this);
    m_rotateCounterClockwiseAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_R));
    m_rotateCounterClockwiseAction->setStatusTip(tr("Rotate page 90° counter-clockwise"));
    m_rotateCounterClockwiseAction->setToolTip(tr("Rotate counter-clockwise (Ctrl+Shift+R)"));
    connect(m_rotateCounterClockwiseAction, &QAction::triggered, this, &MainWindow::rotateCounterClockwise);

    m_rotate180Action = new QAction(tr("Rotate &180°"), this);
    m_rotate180Action->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_U));
    m_rotate180Action->setStatusTip(tr("Rotate page 180°"));
    connect(m_rotate180Action, &QAction::triggered, this, &MainWindow::rotate180);

    m_resetRotationAction = new QAction(tr("&Reset Rotation"), this);
    m_resetRotationAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_0));
    m_resetRotationAction->setStatusTip(tr("Reset page rotation to normal"));
    connect(m_resetRotationAction, &QAction::triggered, this, &MainWindow::resetRotation);

    // View mode actions
    m_singlePageModeAction = new QAction(tr("&Single Page"), this);
    m_singlePageModeAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_1));
    m_singlePageModeAction->setStatusTip(tr("Display one page at a time"));
    m_singlePageModeAction->setToolTip(tr("Single page mode (Ctrl+1)"));
    m_singlePageModeAction->setCheckable(true);
    m_singlePageModeAction->setChecked(true);
    connect(m_singlePageModeAction, &QAction::triggered, this, &MainWindow::setSinglePageMode);

    m_continuousPageModeAction = new QAction(tr("&Continuous"), this);
    m_continuousPageModeAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_2));
    m_continuousPageModeAction->setStatusTip(tr("Display pages continuously"));
    m_continuousPageModeAction->setToolTip(tr("Continuous page mode (Ctrl+2)"));
    m_continuousPageModeAction->setCheckable(true);
    connect(m_continuousPageModeAction, &QAction::triggered, this, &MainWindow::setContinuousPageMode);

    m_facingPageModeAction = new QAction(tr("&Facing Pages"), this);
    m_facingPageModeAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_3));
    m_facingPageModeAction->setStatusTip(tr("Display two pages side by side"));
    m_facingPageModeAction->setToolTip(tr("Facing pages mode (Ctrl+3)"));
    m_facingPageModeAction->setCheckable(true);
    connect(m_facingPageModeAction, &QAction::triggered, this, &MainWindow::setFacingPageMode);

    // Performance actions
    m_clearCacheAction = new QAction(tr("&Clear Cache"), this);
    m_clearCacheAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_Delete));
    m_clearCacheAction->setStatusTip(tr("Clear all cached pages to free memory"));
    connect(m_clearCacheAction, &QAction::triggered, this, &MainWindow::clearCache);

    m_showMemoryUsageAction = new QAction(tr("&Memory Usage..."), this);
    m_showMemoryUsageAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_M));
    m_showMemoryUsageAction->setStatusTip(tr("Show current memory usage"));
    connect(m_showMemoryUsageAction, &QAction::triggered, this, &MainWindow::showMemoryUsage);

    // Export actions
    m_exportCurrentPageAction = new QAction(tr("Export &Current Page..."), this);
    m_exportCurrentPageAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_E));
    m_exportCurrentPageAction->setStatusTip(tr("Export current page as image"));
    connect(m_exportCurrentPageAction, &QAction::triggered, this, &MainWindow::exportCurrentPage);

    m_exportAllPagesAction = new QAction(tr("Export &All Pages..."), this);
    m_exportAllPagesAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_E));
    m_exportAllPagesAction->setStatusTip(tr("Export all pages as images"));
    connect(m_exportAllPagesAction, &QAction::triggered, this, &MainWindow::exportAllPages);

    m_exportPageRangeAction = new QAction(tr("Export Page &Range..."), this);
    m_exportPageRangeAction->setStatusTip(tr("Export a range of pages as images"));
    connect(m_exportPageRangeAction, &QAction::triggered, this, &MainWindow::exportPageRange);

    // Print actions
    m_printDocumentAction = new QAction(tr("&Print Document..."), this);
    m_printDocumentAction->setShortcut(QKeySequence::Print);
    m_printDocumentAction->setStatusTip(tr("Print the entire document"));
    connect(m_printDocumentAction, &QAction::triggered, this, &MainWindow::printDocument);

    m_printCurrentPageAction = new QAction(tr("Print &Current Page..."), this);
    m_printCurrentPageAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_P));
    m_printCurrentPageAction->setStatusTip(tr("Print only the current page"));
    connect(m_printCurrentPageAction, &QAction::triggered, this, &MainWindow::printCurrentPage);

    m_printPreviewAction = new QAction(tr("Print Pre&view..."), this);
    m_printPreviewAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_V));
    m_printPreviewAction->setStatusTip(tr("Preview document before printing"));
    m_printPreviewAction->setToolTip(tr("Print preview (Ctrl+Shift+V)"));
    connect(m_printPreviewAction, &QAction::triggered, this, &MainWindow::showPrintPreview);

    // Settings action
    m_settingsAction = new QAction(tr("&Preferences..."), this);
    m_settingsAction->setShortcut(QKeySequence::Preferences);
    m_settingsAction->setStatusTip(tr("Configure application settings"));
    connect(m_settingsAction, &QAction::triggered, this, &MainWindow::showSettings);

    // Help action with F1 key support
    m_helpAction = new QAction(tr("&Help"), this);
    m_helpAction->setShortcut(QKeySequence::HelpContents);
    m_helpAction->setStatusTip(tr("Show application help and documentation"));
    connect(m_helpAction, &QAction::triggered, this, &MainWindow::showHelp);

    // Document info
    m_documentInfoAction = new QAction(tr("Document &Properties..."), this);
    m_documentInfoAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_I));
    m_documentInfoAction->setStatusTip(tr("Show document properties and metadata"));
    m_documentInfoAction->setToolTip(tr("Document properties (Ctrl+I)"));
    connect(m_documentInfoAction, &QAction::triggered, this, &MainWindow::showDocumentInfo);

    // Annotation actions
    m_undoAction = new QAction(tr("&Undo"), this);
    m_undoAction->setShortcut(QKeySequence::Undo);
    m_undoAction->setStatusTip(tr("Undo last annotation action"));
    m_undoAction->setEnabled(false);
    connect(m_undoAction, &QAction::triggered, this, &MainWindow::undoAnnotation);

    m_redoAction = new QAction(tr("&Redo"), this);
    m_redoAction->setShortcut(QKeySequence::Redo);
    m_redoAction->setStatusTip(tr("Redo last undone annotation action"));
    m_redoAction->setEnabled(false);
    connect(m_redoAction, &QAction::triggered, this, &MainWindow::redoAnnotation);

    // Search annotations action
    m_searchAnnotationsAction = new QAction(tr("&Search Annotations..."), this);
    m_searchAnnotationsAction->setShortcut(QKeySequence("Ctrl+Shift+F"));
    m_searchAnnotationsAction->setStatusTip(tr("Search and filter annotations"));
    m_searchAnnotationsAction->setEnabled(false);
    connect(m_searchAnnotationsAction, &QAction::triggered, this, &MainWindow::searchAnnotations);

    // Copy annotations action
    m_copyAnnotationsAction = new QAction(tr("&Copy Annotations"), this);
    m_copyAnnotationsAction->setShortcut(QKeySequence("Ctrl+Shift+C"));
    m_copyAnnotationsAction->setStatusTip(tr("Copy selected annotations"));
    m_copyAnnotationsAction->setEnabled(false);
    connect(m_copyAnnotationsAction, &QAction::triggered, this, &MainWindow::copyAnnotations);

    // Paste annotations action
    m_pasteAnnotationsAction = new QAction(tr("&Paste Annotations"), this);
    m_pasteAnnotationsAction->setShortcut(QKeySequence("Ctrl+Shift+V"));
    m_pasteAnnotationsAction->setStatusTip(tr("Paste annotations from clipboard"));
    m_pasteAnnotationsAction->setEnabled(false);
    connect(m_pasteAnnotationsAction, &QAction::triggered, this, &MainWindow::pasteAnnotations);
}

// Traditional menu system replaced with ElaMainWindow navigation
// void MainWindow::createMenus() - Commented out for ElaMainWindow compatibility

// Traditional toolbar system replaced with ElaMainWindow ribbon-style navigation
// void MainWindow::createToolBar() - Commented out for ElaMainWindow compatibility
// The toolbar functionality is now handled in setupRibbonNavigation()

void MainWindow::createAnnotationToolbar()
{
    // Create annotation toolbar
    m_annotationToolbar = new AnnotationToolbar(this);

    // Add as a modern ElaToolBar to the main window
    ElaToolBar* annotationToolBar = new ElaToolBar(tr("Annotations"), this);
    annotationToolBar->addWidget(m_annotationToolbar);
    addToolBar(Qt::TopToolBarArea, annotationToolBar);

    // Connect annotation toolbar signals
    connect(m_annotationToolbar, &AnnotationToolbar::toolChanged,
            this, &MainWindow::onAnnotationToolChanged);
    connect(m_annotationToolbar, &AnnotationToolbar::colorChanged,
            this, &MainWindow::onAnnotationColorChanged);
    connect(m_annotationToolbar, &AnnotationToolbar::opacityChanged,
            this, &MainWindow::onAnnotationOpacityChanged);
    connect(m_annotationToolbar, &AnnotationToolbar::lineWidthChanged,
            this, &MainWindow::onAnnotationLineWidthChanged);
    connect(m_annotationToolbar, &AnnotationToolbar::deleteSelectedAnnotations,
            this, &MainWindow::onDeleteSelectedAnnotations);
    connect(m_annotationToolbar, &AnnotationToolbar::copySelectedAnnotations,
            this, &MainWindow::onCopySelectedAnnotations);
    connect(m_annotationToolbar, &AnnotationToolbar::pasteAnnotations,
            this, &MainWindow::onPasteAnnotations);

    // Initially disable annotation tools until a document is loaded
    m_annotationToolbar->setAnnotationToolsEnabled(false);
}

void MainWindow::createStatusBar()
{
    // Create modern ElaStatusBar
    ElaStatusBar* modernStatusBar = new ElaStatusBar(this);

    // Progress bar for operations using Ela styling
    m_progressBar = new ElaProgress(this);
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    m_progressBar->setVisible(false);
    m_progressBar->setTextVisible(false);
    m_progressBar->setMaximumWidth(120);
    m_progressBar->setMaximumHeight(16);
    m_progressBar->setToolTip(tr("Operation progress"));

    // Add permanent widgets to modern status bar
    modernStatusBar->addPermanentWidget(m_progressBar);

    // Add a label for document information using Ela components
    ElaText* docInfoLabel = new ElaText(this);
    docInfoLabel->setObjectName("docInfoLabel");
    docInfoLabel->setMinimumWidth(150);
    docInfoLabel->setTextPixelSize(11);
    modernStatusBar->addPermanentWidget(docInfoLabel);

    // Set the modern status bar
    setStatusBar(modernStatusBar);

    modernStatusBar->showMessage(tr("Ready. Open a PDF file to begin."));
}

void MainWindow::openPdf()
{
    trackUserAction("Open PDF dialog requested");
    const QString filePath = QFileDialog::getOpenFileName(this, tr("Open PDF File"), QDir::homePath(), tr("PDF Files (*.pdf)"));
    if (filePath.isEmpty()) {
        trackUserAction("Open PDF dialog cancelled");
        return;
    }

    trackUserAction(QString("Opening PDF file: %1").arg(QFileInfo(filePath).fileName()));
    openPdfFile(filePath);
}

void MainWindow::openPdfFile(const QString& filePath)
{
    DocumentTab* currentTab = getCurrentTab();

    // If current tab is empty, use it; otherwise create a new tab
    if (!currentTab || currentTab->isDocumentLoaded()) {
        currentTab = createNewTab();
    }

    // Load document in the tab
    currentTab->loadDocument(filePath);

    // Add to recent files
    addToRecentFiles(filePath);

    // Hide welcome screen when loading a document
    hideWelcomeScreen();

    // Show loading overlay with animation
    QString fileName = QFileInfo(filePath).fileName();
    m_loadingOverlay->showLoading(tr("Loading %1...").arg(fileName));
    statusBar()->showMessage(tr("Loading %1...").arg(fileName));
}





void MainWindow::onPreviewProgressUpdated(int pagesRendered, int totalPages)
{
    m_progressBar->setValue(pagesRendered);
    if (pagesRendered == totalPages) {
        m_progressBar->setVisible(false);
        statusBar()->showMessage(tr("Ready."), 3000);
    }
}



void MainWindow::updateUiState()
{
    // Use throttled UI updates for better performance
    scheduleUiUpdate();
}

void MainWindow::scheduleUiUpdate()
{
    if (!m_uiUpdatePending) {
        m_uiUpdatePending = true;
        m_uiUpdateTimer->start();
    }
}

void MainWindow::performScheduledUiUpdate()
{
    m_uiUpdatePending = false;

    DocumentTab* currentTab = getCurrentTab();
    const bool hasDocument = currentTab && currentTab->isDocumentLoaded();
    const int pageCount = hasDocument ? currentTab->getPageCount() : 0;
    const int currentPage = hasDocument ? currentTab->getCurrentPage() : 0;

    m_openAction->setEnabled(true); // Always allow opening a new file
    m_closeTabAction->setEnabled(m_tabWidget->count() > 1 || hasDocument);

    m_nextPageAction->setEnabled(hasDocument && currentPage < pageCount - 1);
    m_prevPageAction->setEnabled(hasDocument && currentPage > 0);
    m_firstPageAction->setEnabled(hasDocument && currentPage > 0);
    m_lastPageAction->setEnabled(hasDocument && currentPage < pageCount - 1);

    m_zoomInAction->setEnabled(hasDocument);
    m_zoomOutAction->setEnabled(hasDocument);
    m_zoom100Action->setEnabled(hasDocument);
    m_fitToWindowAction->setEnabled(hasDocument);
    m_fitToWidthAction->setEnabled(hasDocument);
    m_toggleThumbnailsAction->setEnabled(hasDocument);
    m_toggleOutlineAction->setEnabled(hasDocument);
    m_fullScreenAction->setEnabled(hasDocument);
    m_selectionZoomAction->setEnabled(hasDocument);
    m_magnifierAction->setEnabled(hasDocument);
    m_customZoomAction->setEnabled(hasDocument);
    m_searchAction->setEnabled(hasDocument);
    m_documentInfoAction->setEnabled(hasDocument);
    m_printPreviewAction->setEnabled(hasDocument);

    m_pageSpinBox->setEnabled(hasDocument);

    // Enable/disable annotation tools
    if (m_annotationToolbar) {
        m_annotationToolbar->setAnnotationToolsEnabled(hasDocument);
    }

    // Update undo/redo actions
    updateUndoRedoActions();

    if (hasDocument) {
        m_pageSpinBox->blockSignals(true);
        m_pageSpinBox->setRange(1, pageCount);
        m_pageSpinBox->setValue(currentPage + 1);
        m_pageSpinBox->blockSignals(false);
        m_pageCountLabel->setText(tr("of %1").arg(pageCount));
        setWindowTitle(tr("Optimized PDF Viewer - %1").arg(currentTab->getFileName()));

        // Update document info in status bar
        ElaText* docInfoLabel = statusBar()->findChild<ElaText*>("docInfoLabel");
        if (docInfoLabel) {
            double zoomFactor = currentTab->getZoomFactor();
            QString docInfo = tr("Page %1/%2 | Zoom: %3%")
                .arg(currentPage + 1)
                .arg(pageCount)
                .arg(qRound(zoomFactor * 100));
            docInfoLabel->setText(docInfo);
        }
    } else {
        m_pageSpinBox->setRange(0, 0);
        m_pageSpinBox->setValue(0);
        m_pageCountLabel->setText(tr("of 0"));
        setWindowTitle(tr("Optimized PDF Viewer"));

        // Clear document info
        ElaText* docInfoLabel = statusBar()->findChild<ElaText*>("docInfoLabel");
        if (docInfoLabel) {
            docInfoLabel->setText(tr("No document"));
        }
    }
}

void MainWindow::nextPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->nextPage();
        updateUiState();
    }
}

void MainWindow::previousPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->previousPage();
        updateUiState();
    }
}

void MainWindow::zoomIn()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        double currentZoom = currentTab->getZoomFactor();
        double newZoom;

        // Use smart zoom increments based on current zoom level for better UX
        if (currentZoom < 0.25) {
            newZoom = currentZoom * 1.5; // 50% increase for very low zoom
        } else if (currentZoom < 0.5) {
            newZoom = currentZoom * 1.4; // 40% increase for low zoom
        } else if (currentZoom < 1.0) {
            newZoom = currentZoom * 1.3; // 30% increase for medium-low zoom
        } else if (currentZoom < 2.0) {
            newZoom = currentZoom * 1.25; // 25% increase for medium zoom
        } else if (currentZoom < 4.0) {
            newZoom = currentZoom * 1.2; // 20% increase for high zoom
        } else {
            newZoom = currentZoom * 1.15; // 15% increase for very high zoom
        }

        newZoom = qMin(newZoom, 10.0); // Cap at 1000%
        currentTab->setZoomFactor(newZoom);
        updateZoomControls();
        statusBar()->showMessage(tr("Zoomed in to %1%").arg(qRound(newZoom * 100)), 2000);
    }
}

void MainWindow::zoomOut()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        double currentZoom = currentTab->getZoomFactor();
        double newZoom;

        // Use smart zoom decrements based on current zoom level for better UX
        if (currentZoom <= 0.25) {
            newZoom = currentZoom / 1.5; // Proportional decrease for very low zoom
        } else if (currentZoom <= 0.5) {
            newZoom = currentZoom / 1.4; // Proportional decrease for low zoom
        } else if (currentZoom <= 1.0) {
            newZoom = currentZoom / 1.3; // Proportional decrease for medium-low zoom
        } else if (currentZoom <= 2.0) {
            newZoom = currentZoom / 1.25; // Proportional decrease for medium zoom
        } else if (currentZoom <= 4.0) {
            newZoom = currentZoom / 1.2; // Proportional decrease for high zoom
        } else {
            newZoom = currentZoom / 1.15; // Proportional decrease for very high zoom
        }

        newZoom = qMax(newZoom, 0.1); // Minimum 10%
        currentTab->setZoomFactor(newZoom);
        updateZoomControls();
        statusBar()->showMessage(tr("Zoomed out to %1%").arg(qRound(newZoom * 100)), 2000);
    }
}

void MainWindow::jumpToPage(int page)
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->setCurrentPage(page - 1); // Spinbox is 1-based
        updateUiState();
    }
}

void MainWindow::fitToWindow()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->fitToWindow();
        updateZoomControls();
    }
}

void MainWindow::fitToWidth()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->fitToWidth();
        updateZoomControls();
    }
}

void MainWindow::firstPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->firstPage();
        updateUiState();
    }
}

void MainWindow::lastPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->lastPage();
        updateUiState();
    }
}

void MainWindow::zoom100()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->zoom100();
        updateZoomControls();
        statusBar()->showMessage(tr("Zoom: 100%"), 2000);
    }
}

void MainWindow::resizeEvent(QResizeEvent* event)
{
    ElaWindow::resizeEvent(event);

    // Handle responsive layout changes based on window size
    const QSize newSize = event->size();
    const int width = newSize.width();
    const int height = newSize.height();

    // Define responsive breakpoints
    const int MOBILE_BREAKPOINT = 600;
    const int LARGE_DESKTOP_BREAKPOINT = 1600;

    // Responsive panel management
    handleResponsivePanels(width);

    // Responsive navigation bar
    handleResponsiveNavigation(width);

    // Responsive toolbar layout
    handleResponsiveToolbar(width);

    // Responsive tab widget
    handleResponsiveTabWidget(width, height);

    // Update current tab's fit mode if it's set to fit to window
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        // Trigger a fit mode update for responsive PDF viewing
        updateUiState();

        // Auto-adjust zoom for better readability on different screen sizes
        if (width < MOBILE_BREAKPOINT) {
            // On mobile, prefer fit-to-width for better readability
            currentTab->fitToWidth();
        } else if (width >= LARGE_DESKTOP_BREAKPOINT) {
            // On large screens, show more content
            if (currentTab->getZoomFactor() < 1.0) {
                currentTab->setZoomFactor(1.0);
            }
        }
    }
}

void MainWindow::closeEvent(QCloseEvent* event)
{
    // Save session before closing
    saveSession();

    // Accept the close event
    ElaWindow::closeEvent(event);
}

// Responsive layout helper methods
void MainWindow::handleResponsivePanels(int width)
{
    const int MOBILE_BREAKPOINT = 600;
    const int TABLET_BREAKPOINT = 900;

    if (width < MOBILE_BREAKPOINT) {
        // Mobile: Hide all side panels for maximum content space
        if (m_thumbnailsDock && m_thumbnailsDock->isVisible()) {
            m_thumbnailsDock->hide();
        }
        if (m_outlineDock && m_outlineDock->isVisible()) {
            m_outlineDock->hide();
        }
        if (m_searchResultsDock && m_searchResultsDock->isVisible()) {
            m_searchResultsDock->hide();
        }
    } else if (width < TABLET_BREAKPOINT) {
        // Tablet: Show only essential panels, make them narrower
        if (m_thumbnailsDock) {
            m_thumbnailsDock->setMaximumWidth(150);
        }
        if (m_outlineDock) {
            m_outlineDock->setMaximumWidth(200);
        }
    } else {
        // Desktop: Restore normal panel widths
        if (m_thumbnailsDock) {
            m_thumbnailsDock->setMaximumWidth(250);
        }
        if (m_outlineDock) {
            m_outlineDock->setMaximumWidth(300);
        }
    }
}

void MainWindow::handleResponsiveNavigation(int width)
{
    const int MOBILE_BREAKPOINT = 600;
    const int TABLET_BREAKPOINT = 900;

    if (width < MOBILE_BREAKPOINT) {
        // Mobile: Use minimal navigation
        setNavigationBarDisplayMode(ElaNavigationType::Minimal);
    } else if (width < TABLET_BREAKPOINT) {
        // Tablet: Use compact navigation
        setNavigationBarDisplayMode(ElaNavigationType::Compact);
    } else {
        // Desktop: Use full navigation
        setNavigationBarDisplayMode(ElaNavigationType::Maximal);
    }
}

void MainWindow::handleResponsiveToolbar(int width)
{
    const int MOBILE_BREAKPOINT = 600;
    const int TABLET_BREAKPOINT = 900;

    // Find ribbon interface and adjust its layout
    if (auto* ribbonInterface = findChild<RibbonInterface*>()) {
        if (width < MOBILE_BREAKPOINT) {
            // Mobile: Hide less important ribbon sections
            ribbonInterface->setCompactMode(true);
        } else if (width < TABLET_BREAKPOINT) {
            // Tablet: Show essential sections only
            ribbonInterface->setCompactMode(false);
        } else {
            // Desktop: Show all sections
            ribbonInterface->setCompactMode(false);
        }
    }

    // Adjust zoom controls layout
    if (m_zoomSlider && m_zoomComboBox) {
        if (width < MOBILE_BREAKPOINT) {
            // Mobile: Hide zoom slider, keep combo box only
            m_zoomSlider->hide();
            m_zoomComboBox->show();
        } else {
            // Tablet and Desktop: Show both controls
            m_zoomSlider->show();
            m_zoomComboBox->show();
        }
    }
}

void MainWindow::handleResponsiveTabWidget(int width, int height)
{
    Q_UNUSED(height); // Reserved for future height-based responsive features
    const int MOBILE_BREAKPOINT = 600;

    if (m_tabWidget) {
        if (width < MOBILE_BREAKPOINT) {
            // Mobile: Reduce tab widget margins for more content space
            m_tabWidget->setContentsMargins(2, 2, 2, 2);

            // Hide tab bar if only one tab is open
            if (m_tabWidget->count() <= 1) {
                m_tabWidget->tabBar()->hide();
            } else {
                m_tabWidget->tabBar()->show();
            }
        } else {
            // Desktop: Normal margins and always show tab bar
            m_tabWidget->setContentsMargins(5, 5, 5, 5);
            m_tabWidget->tabBar()->show();
        }
    }

    // Adjust search bar layout for mobile
    if (m_searchBar) {
        if (width < MOBILE_BREAKPOINT) {
            // Mobile: Make search bar more compact
            m_searchBar->setMaximumHeight(40);
        } else {
            // Desktop: Normal search bar height
            m_searchBar->setMaximumHeight(50);
        }
    }
}

void MainWindow::createThumbnailsPanel()
{
    // Create the thumbnails dock widget with improved styling
    m_thumbnailsDock = new ElaDock(tr("Thumbnails"), this);
    m_thumbnailsDock->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
    m_thumbnailsDock->setStyleSheet(
        "QDockWidget { "
        "    font-size: 12px; "
        "    font-weight: bold; "
        "} "
        "QDockWidget::title { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f0f0, stop:1 #e0e0e0); "
        "    padding: 4px; "
        "    border: 1px solid #c0c0c0; "
        "}"
    );

    // Create the thumbnails list widget with improved styling
    m_thumbnailsList = new ElaListWidget(this);
    m_thumbnailsList->setViewMode(QListWidget::IconMode);
    m_thumbnailsList->setIconSize(QSize(100, 140));
    m_thumbnailsList->setSpacing(8);
    m_thumbnailsList->setResizeMode(QListWidget::Adjust);
    m_thumbnailsList->setMovement(QListWidget::Static);
    m_thumbnailsList->setSelectionMode(QAbstractItemView::SingleSelection);
    m_thumbnailsList->setStyleSheet(
        "QListWidget { "
        "    background: white; "
        "    border: 1px solid #d0d0d0; "
        "    padding: 4px; "
        "} "
        "QListWidget::item { "
        "    border: 1px solid #e0e0e0; "
        "    border-radius: 4px; "
        "    padding: 4px; "
        "    margin: 2px; "
        "} "
        "QListWidget::item:selected { "
        "    background: #e3f2fd; "
        "    border: 2px solid #2196f3; "
        "} "
        "QListWidget::item:hover { "
        "    background: #f5f5f5; "
        "    border: 1px solid #c0c0c0; "
        "}"
    );

    connect(m_thumbnailsList, &QListWidget::itemClicked, this, &MainWindow::onThumbnailClicked);

    m_thumbnailsDock->setWidget(m_thumbnailsList);
    addDockWidget(Qt::LeftDockWidgetArea, m_thumbnailsDock);

    // Connect the dock widget visibility to the action
    connect(m_thumbnailsDock, &QDockWidget::visibilityChanged,
            m_toggleThumbnailsAction, &QAction::setChecked);
}

void MainWindow::createOutlinePanel()
{
    // Create the outline dock widget with improved styling
    m_outlineDock = new ElaDock(tr("Document Outline"), this);
    m_outlineDock->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
    m_outlineDock->setStyleSheet(
        "QDockWidget { "
        "    font-size: 12px; "
        "    font-weight: bold; "
        "} "
        "QDockWidget::title { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f0f0, stop:1 #e0e0e0); "
        "    padding: 4px; "
        "    border: 1px solid #c0c0c0; "
        "}"
    );

    // Create the outline tree widget with improved styling
    m_outlineTree = new ElaTreeWidget(this);
    m_outlineTree->setHeaderLabel(tr("Contents"));
    m_outlineTree->setRootIsDecorated(true);
    m_outlineTree->setAlternatingRowColors(true);
    m_outlineTree->setSelectionMode(QAbstractItemView::SingleSelection);
    m_outlineTree->setContextMenuPolicy(Qt::CustomContextMenu);
    m_outlineTree->setStyleSheet(
        "QTreeWidget { "
        "    background: white; "
        "    border: 1px solid #d0d0d0; "
        "    font-size: 11px; "
        "} "
        "QTreeWidget::item { "
        "    padding: 2px; "
        "    border: none; "
        "} "
        "QTreeWidget::item:selected { "
        "    background: #e3f2fd; "
        "    color: #1976d2; "
        "} "
        "QTreeWidget::item:hover { "
        "    background: #f5f5f5; "
        "} "
        "QTreeWidget::branch:has-children:!has-siblings:closed, "
        "QTreeWidget::branch:closed:has-children:has-siblings { "
        "    border-image: none; "
        "    image: url(:/icons/branch-closed.png); "
        "} "
        "QTreeWidget::branch:open:has-children:!has-siblings, "
        "QTreeWidget::branch:open:has-children:has-siblings { "
        "    border-image: none; "
        "    image: url(:/icons/branch-open.png); "
        "}"
    );

    connect(m_outlineTree, &QTreeWidget::itemClicked, this, &MainWindow::onOutlineItemClicked);
    connect(m_outlineTree, &QTreeWidget::customContextMenuRequested, this, &MainWindow::onOutlineContextMenu);

    // Create a widget to hold the tree and toolbar
    QWidget* outlineWidget = new QWidget();
    QVBoxLayout* outlineLayout = new QVBoxLayout(outlineWidget);
    outlineLayout->setContentsMargins(2, 2, 2, 2);
    outlineLayout->setSpacing(2);

    // Create toolbar for outline actions
    QHBoxLayout* toolbarLayout = new QHBoxLayout();
    toolbarLayout->setContentsMargins(0, 0, 0, 0);

    ElaPushButton* expandAllBtn = new ElaPushButton(tr("Expand All"));
    expandAllBtn->setMaximumHeight(24);
    expandAllBtn->setToolTip(tr("Expand all outline items"));
    expandAllBtn->setStyleSheet(
        "ElaPushButton { "
        "    background: #f8f9fa; "
        "    border: 1px solid #dee2e6; "
        "    border-radius: 4px; "
        "    color: #495057; "
        "    font-size: 11px; "
        "    padding: 2px 8px; "
        "} "
        "ElaPushButton:hover { "
        "    background: #e9ecef; "
        "    border: 1px solid #adb5bd; "
        "}"
    );
    connect(expandAllBtn, &ElaPushButton::clicked, m_outlineTree, &QTreeWidget::expandAll);

    ElaPushButton* collapseAllBtn = new ElaPushButton(tr("Collapse All"));
    collapseAllBtn->setMaximumHeight(24);
    collapseAllBtn->setToolTip(tr("Collapse all outline items"));
    collapseAllBtn->setStyleSheet(
        "ElaPushButton { "
        "    background: #f8f9fa; "
        "    border: 1px solid #dee2e6; "
        "    border-radius: 4px; "
        "    color: #495057; "
        "    font-size: 11px; "
        "    padding: 2px 8px; "
        "} "
        "ElaPushButton:hover { "
        "    background: #e9ecef; "
        "    border: 1px solid #adb5bd; "
        "}"
    );
    connect(collapseAllBtn, &ElaPushButton::clicked, m_outlineTree, &QTreeWidget::collapseAll);

    toolbarLayout->addWidget(expandAllBtn);
    toolbarLayout->addWidget(collapseAllBtn);
    toolbarLayout->addStretch();

    outlineLayout->addLayout(toolbarLayout);
    outlineLayout->addWidget(m_outlineTree);

    m_outlineDock->setWidget(outlineWidget);
    addDockWidget(Qt::RightDockWidgetArea, m_outlineDock);

    // Connect the dock widget visibility to the action
    connect(m_outlineDock, &QDockWidget::visibilityChanged,
            m_toggleOutlineAction, &QAction::setChecked);
}

void MainWindow::createSearchResultsPanel()
{
    // Create the search results dock widget
    m_searchResultsDock = new ElaDock(tr("Search Results"), this);
    m_searchResultsDock->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);

    // Create the search results panel
    m_searchResultsPanel = new SearchResultsPanel(this);
    m_searchResultsDock->setWidget(m_searchResultsPanel);

    // Add to the right dock area and hide by default
    addDockWidget(Qt::RightDockWidgetArea, m_searchResultsDock);
    m_searchResultsDock->hide();

    // Connect signals
    connect(m_searchResultsPanel, &SearchResultsPanel::resultSelected,
            this, &MainWindow::onSearchResultSelected);
    connect(m_searchResultsPanel, &SearchResultsPanel::searchRequested,
            this, &MainWindow::onSearchRequested);
    connect(m_searchResultsPanel, &SearchResultsPanel::panelClosed,
            this, &MainWindow::onSearchPanelClosed);

    // Connect the dock widget visibility to the action
    connect(m_searchResultsDock, &QDockWidget::visibilityChanged,
            m_toggleSearchResultsAction, &QAction::setChecked);
}

void MainWindow::updateThumbnails()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) {
        m_thumbnailsList->clear();
        return;
    }

    const int pageCount = currentTab->getPageCount();
    m_thumbnailsList->clear();

    for (int i = 0; i < pageCount; ++i) {
        QListWidgetItem* item = new QListWidgetItem(m_thumbnailsList);
        item->setText(tr("Page %1").arg(i + 1));
        item->setData(Qt::UserRole, i); // Store page number
        item->setSizeHint(QSize(130, 180));

        // Request preview for this page
        currentTab->getPdfController()->requestPreview(i);
    }
}

void MainWindow::updateOutline()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) {
        m_outlineTree->clear();
        return;
    }

    // Get the outline from the controller
    QList<OutlineItem> outline = currentTab->getPdfController()->getDocumentOutline();

    m_outlineTree->clear();

    // Populate the tree
    for (const OutlineItem& item : outline) {
        addOutlineItemToTree(nullptr, item);
    }

    // Expand the first level by default
    m_outlineTree->expandToDepth(0);
}

void MainWindow::addOutlineItemToTree(QTreeWidgetItem* parent, const OutlineItem& item)
{
    QTreeWidgetItem* treeItem;
    if (parent) {
        treeItem = new QTreeWidgetItem(parent);
    } else {
        treeItem = new QTreeWidgetItem(m_outlineTree);
    }

    treeItem->setText(0, item.title);
    treeItem->setData(0, Qt::UserRole, item.pageNumber);

    // Add children recursively
    for (const OutlineItem& child : item.children) {
        addOutlineItemToTree(treeItem, child);
    }

    // Set expansion state
    treeItem->setExpanded(item.isOpen);
}

void MainWindow::toggleThumbnails()
{
    m_thumbnailsDock->setVisible(!m_thumbnailsDock->isVisible());
}

void MainWindow::toggleOutline()
{
    m_outlineDock->setVisible(!m_outlineDock->isVisible());
}

void MainWindow::toggleSearchResults()
{
    if (m_searchResultsDock) {
        m_searchResultsDock->setVisible(!m_searchResultsDock->isVisible());
    }
}

void MainWindow::onOutlineItemClicked(QTreeWidgetItem* item, int column)
{
    Q_UNUSED(column);

    if (!item) return;

    // Get the page number from the item data
    bool ok;
    int pageNum = item->data(0, Qt::UserRole).toInt(&ok);

    DocumentTab* currentTab = getCurrentTab();
    if (ok && pageNum >= 0 && currentTab && pageNum < currentTab->getPageCount()) {
        currentTab->setCurrentPage(pageNum);
        updateUiState();
        statusBar()->showMessage(tr("Navigated to %1").arg(item->text(0)), 3000);
    }
}

void MainWindow::onOutlineContextMenu(const QPoint& pos)
{
    QTreeWidgetItem* item = m_outlineTree->itemAt(pos);
    if (!item) return;

    QMenu contextMenu(this);

    // Navigate to item action
    QAction* navigateAction = contextMenu.addAction(tr("Go to \"%1\"").arg(item->text(0)));
    connect(navigateAction, &QAction::triggered, [this, item]() {
        onOutlineItemClicked(item, 0);
    });

    contextMenu.addSeparator();

    // Expand/Collapse actions
    if (item->childCount() > 0) {
        if (item->isExpanded()) {
            QAction* collapseAction = contextMenu.addAction(tr("Collapse"));
            connect(collapseAction, &QAction::triggered, [item]() {
                item->setExpanded(false);
            });

            QAction* collapseAllAction = contextMenu.addAction(tr("Collapse All"));
            connect(collapseAllAction, &QAction::triggered, [this]() {
                m_outlineTree->collapseAll();
            });
        } else {
            QAction* expandAction = contextMenu.addAction(tr("Expand"));
            connect(expandAction, &QAction::triggered, [item]() {
                item->setExpanded(true);
            });
        }

        QAction* expandAllAction = contextMenu.addAction(tr("Expand All"));
        connect(expandAllAction, &QAction::triggered, [this]() {
            m_outlineTree->expandAll();
        });
    }

    contextMenu.exec(m_outlineTree->mapToGlobal(pos));
}

void MainWindow::onThumbnailClicked(QListWidgetItem* item)
{
    if (!item) return;

    const int pageNum = item->data(Qt::UserRole).toInt();
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->setCurrentPage(pageNum);
        updateUiState();

        // Update selection in thumbnails
        for (int i = 0; i < m_thumbnailsList->count(); ++i) {
            QListWidgetItem* listItem = m_thumbnailsList->item(i);
            if (listItem) {
                listItem->setSelected(i == pageNum);
            }
        }
    }
}

void MainWindow::createSearchBar()
{
    // Create search bar widget with improved styling
    m_searchBar = new QWidget(this);
    m_searchBar->setVisible(false);
    m_searchBar->setStyleSheet(
        "QWidget { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef); "
        "    border-bottom: 1px solid #dee2e6; "
        "    padding: 4px; "
        "}"
    );

    QHBoxLayout* searchLayout = new QHBoxLayout(m_searchBar);
    searchLayout->setContentsMargins(8, 6, 8, 6);
    searchLayout->setSpacing(6);

    // Search input with improved styling
    m_searchEdit = new ElaEdit(this);
    m_searchEdit->setPlaceholderText(tr("Search text..."));
    m_searchEdit->setMinimumWidth(200);
    m_searchEdit->setStyleSheet(
        "QLineEdit { "
        "    padding: 4px 8px; "
        "    border: 2px solid #ced4da; "
        "    border-radius: 4px; "
        "    background: white; "
        "    font-size: 12px; "
        "} "
        "QLineEdit:focus { "
        "    border: 2px solid #0078d4; "
        "    outline: none; "
        "} "
        "QLineEdit:hover { "
        "    border: 2px solid #adb5bd; "
        "}"
    );
    connect(m_searchEdit, &QLineEdit::returnPressed, this, &MainWindow::searchText);
    connect(m_searchEdit, &QLineEdit::textChanged, this, &MainWindow::searchText);

    // Navigation buttons with improved styling
    m_findPrevButton = new ElaButton(tr("◀"), this);
    m_findPrevButton->setEnabled(false);
    m_findPrevButton->setMaximumWidth(32);
    m_findPrevButton->setToolTip(tr("Previous result (Shift+F3)"));
    m_findPrevButton->setStyleSheet(
        "QPushButton { "
        "    padding: 4px 8px; "
        "    border: 1px solid #ced4da; "
        "    border-radius: 3px; "
        "    background: white; "
        "    font-size: 12px; "
        "} "
        "QPushButton:hover:enabled { "
        "    background: #e9ecef; "
        "    border: 1px solid #adb5bd; "
        "} "
        "QPushButton:disabled { "
        "    color: #6c757d; "
        "    background: #f8f9fa; "
        "}"
    );
    connect(m_findPrevButton, &QPushButton::clicked, this, &MainWindow::findPrevious);

    m_findNextButton = new ElaButton(tr("▶"), this);
    m_findNextButton->setEnabled(false);
    m_findNextButton->setMaximumWidth(32);
    m_findNextButton->setToolTip(tr("Next result (F3)"));
    m_findNextButton->setStyleSheet(m_findPrevButton->styleSheet());
    connect(m_findNextButton, &QPushButton::clicked, this, &MainWindow::findNext);

    // Search options
    m_caseSensitiveCheckBox = new ElaCheck(tr("Case sensitive"), this);
    m_caseSensitiveCheckBox->setToolTip(tr("Match case when searching"));
    connect(m_caseSensitiveCheckBox, &QCheckBox::toggled, this, &MainWindow::searchText);

    m_wholeWordCheckBox = new ElaCheck(tr("Whole words"), this);
    m_wholeWordCheckBox->setToolTip(tr("Match whole words only"));
    connect(m_wholeWordCheckBox, &QCheckBox::toggled, this, &MainWindow::searchText);

    m_highlightAllCheckBox = new ElaCheck(tr("Highlight all"), this);
    m_highlightAllCheckBox->setChecked(true);
    m_highlightAllCheckBox->setToolTip(tr("Highlight all search results"));
    connect(m_highlightAllCheckBox, &QCheckBox::toggled, this, &MainWindow::searchText);

    // Search results label
    m_searchResultsLabel = new ElaLabel(this);
    m_searchResultsLabel->setMinimumWidth(120);
    m_searchResultsLabel->setAlignment(Qt::AlignCenter);

    // Close button
    m_closeSearchButton = new ElaButton(tr("×"), this);
    m_closeSearchButton->setMaximumWidth(30);
    connect(m_closeSearchButton, &QPushButton::clicked, this, &MainWindow::hideSearchBar);

    // Layout with improved organization
    searchLayout->addWidget(new QLabel(tr("Find:"), this));
    searchLayout->addWidget(m_searchEdit);

    // Add separator
    QFrame* separator1 = new QFrame(this);
    separator1->setFrameShape(QFrame::VLine);
    separator1->setFrameShadow(QFrame::Sunken);
    searchLayout->addWidget(separator1);

    // Search options
    searchLayout->addWidget(m_caseSensitiveCheckBox);
    searchLayout->addWidget(m_wholeWordCheckBox);
    searchLayout->addWidget(m_highlightAllCheckBox);

    // Add separator
    QFrame* separator2 = new QFrame(this);
    separator2->setFrameShape(QFrame::VLine);
    separator2->setFrameShadow(QFrame::Sunken);
    searchLayout->addWidget(separator2);

    // Navigation controls
    searchLayout->addWidget(m_findPrevButton);
    searchLayout->addWidget(m_findNextButton);
    searchLayout->addWidget(m_searchResultsLabel);
    searchLayout->addStretch(); // Push close button to the right
    searchLayout->addWidget(m_closeSearchButton);

    // Add search bar to main layout
    QVBoxLayout* mainLayout = new QVBoxLayout();
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    mainLayout->addWidget(m_searchBar);
    mainLayout->addWidget(m_tabWidget);

    QWidget* newCentralWidget = new QWidget(this);
    newCentralWidget->setLayout(mainLayout);
    setCentralWidget(newCentralWidget);

    // Create enhanced search widget (initially hidden)
    m_enhancedSearchWidget = new SearchWidget(this);
    m_enhancedSearchWidget->setSearchHistory(m_searchHistory);
    m_enhancedSearchWidget->setVisible(false);

    // Connect enhanced search widget signals
    connect(m_enhancedSearchWidget, &SearchWidget::searchRequested,
            this, &MainWindow::onEnhancedSearchRequested);
    connect(m_enhancedSearchWidget, &SearchWidget::searchCleared,
            this, &MainWindow::onEnhancedSearchCleared);
    connect(m_enhancedSearchWidget, &SearchWidget::findNext,
            this, &MainWindow::findNext);
    connect(m_enhancedSearchWidget, &SearchWidget::findPrevious,
            this, &MainWindow::findPrevious);
}

void MainWindow::showSearchBar()
{
    m_searchBar->setVisible(true);
    m_searchEdit->setFocus();
    m_searchEdit->selectAll();
}

void MainWindow::hideSearchBar()
{
    m_searchBar->setVisible(false);
    m_searchResults.clear();
    m_currentSearchIndex = -1;
    m_currentSearchTerm.clear();
    requestCurrentPage(); // Refresh to remove highlights
}

void MainWindow::searchText()
{
    const QString searchTerm = m_searchEdit->text().trimmed();
    if (searchTerm.isEmpty()) {
        m_searchResults.clear();
        m_currentSearchIndex = -1;
        m_findNextButton->setEnabled(false);
        m_findPrevButton->setEnabled(false);
        m_searchResultsLabel->setText("");

        // Clear highlights if highlight all is enabled
        if (m_highlightAllResults) {
            requestCurrentPage();
        }
        return;
    }

    m_currentSearchTerm = searchTerm;
    m_caseSensitiveSearch = m_caseSensitiveCheckBox->isChecked();
    m_wholeWordSearch = m_wholeWordCheckBox->isChecked();
    m_highlightAllResults = m_highlightAllCheckBox->isChecked();

    // Start the search
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        statusBar()->showMessage(tr("Searching..."), 0);

        // Use the enhanced search method with whole word support
        currentTab->getPdfController()->performSearch(searchTerm, m_caseSensitiveSearch, m_wholeWordSearch);
    }
}

void MainWindow::findNext()
{
    if (m_searchResults.isEmpty()) return;

    m_currentSearchIndex++;
    if (m_currentSearchIndex >= m_searchResults.size()) {
        m_currentSearchIndex = 0; // Wrap around to beginning
        statusBar()->showMessage(tr("Reached end of search results, wrapping to beginning"), 2000);
    }

    navigateToSearchResult(m_currentSearchIndex);
}

void MainWindow::findPrevious()
{
    if (m_searchResults.isEmpty()) return;

    m_currentSearchIndex--;
    if (m_currentSearchIndex < 0) {
        m_currentSearchIndex = m_searchResults.size() - 1; // Wrap around to end
        statusBar()->showMessage(tr("Reached beginning of search results, wrapping to end"), 2000);
    }

    navigateToSearchResult(m_currentSearchIndex);
}

void MainWindow::addBookmark()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded() || currentTab->getFilePath().isEmpty()) return;

    BookmarkDialog dialog(m_settings, this);
    dialog.setCurrentDocument(currentTab->getFilePath(),
                             currentTab->getCurrentPage(),
                             currentTab->getZoomFactor());

    // Show the dialog and let user add bookmark through the dialog
    dialog.exec();
}

void MainWindow::goToBookmark()
{
    BookmarkDialog dialog(m_settings, this);

    // Set current document info if available
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        dialog.setCurrentDocument(currentTab->getFilePath(),
                                 currentTab->getCurrentPage(),
                                 currentTab->getZoomFactor());
    }

    // Connect to bookmark selection signal
    connect(&dialog, &BookmarkDialog::bookmarkSelected, this,
            [this](const QString& filePath, int pageNumber, double zoomFactor) {
        DocumentTab* currentTab = getCurrentTab();

        // If it's a different file, open it
        if (!currentTab || filePath != currentTab->getFilePath()) {
            openPdfFile(filePath);
            // TODO: Set page and zoom when document loads
            return;
        }

        // Same file, just navigate
        currentTab->setCurrentPage(pageNumber);
        currentTab->setZoomFactor(zoomFactor);
        updateUiState();

        statusBar()->showMessage(tr("Navigated to bookmark"), 3000);
    });

    dialog.exec();
}

void MainWindow::openRecentFile()
{
    QAction* action = qobject_cast<QAction*>(sender());
    if (action) {
        const QString filePath = action->data().toString();
        openPdfFile(filePath);
    }
}

void MainWindow::clearRecentFiles()
{
    m_recentFiles.clear();
    m_settings->remove("recentFiles");
    updateRecentFilesMenu();
    statusBar()->showMessage(tr("Recent files cleared"), 2000);
}

void MainWindow::loadSettings()
{
    // Load recent files
    m_recentFiles = m_settings->value("recentFiles").toStringList();

    // Remove non-existent files
    QStringList validFiles;
    for (const QString& file : m_recentFiles) {
        if (QFile::exists(file)) {
            validFiles.append(file);
        }
    }
    m_recentFiles = validFiles;

    // Load window geometry (if enabled)
    const bool rememberWindow = m_settings->value("rememberWindowState", true).toBool();
    if (rememberWindow) {
        restoreGeometry(m_settings->value("geometry").toByteArray());
        restoreState(m_settings->value("windowState").toByteArray());
    }

    // Load interface settings - re-enabling theme settings only
    // UI-dependent settings still disabled for debugging
    /*
    const bool showThumbnails = m_settings->value("showThumbnails", true).toBool();
    m_thumbnailsDock->setVisible(showThumbnails);
    m_toggleThumbnailsAction->setChecked(showThumbnails);

    const bool showOutline = m_settings->value("showOutline", true).toBool();
    m_outlineDock->setVisible(showOutline);
    m_toggleOutlineAction->setChecked(showOutline);
    */

    // Load theme settings - this might be important for ElaMainWindow
    int themeMode = m_settings->value("themeMode", static_cast<int>(ElaThemeType::Light)).toInt();
    applyTheme(static_cast<ElaThemeType::ThemeMode>(themeMode));

    // Apply other settings - temporarily disabled
    // applySettings();

    // Check if this is the first time running the application
    bool onboardingCompleted = m_settings->value("onboardingCompleted", false).toBool();
    if (!onboardingCompleted) {
        // Start onboarding tour after a short delay
        QTimer::singleShot(2000, this, &MainWindow::startOnboardingTour);
    }
}

void MainWindow::saveSettings()
{
    // Save recent files
    m_settings->setValue("recentFiles", m_recentFiles);

    // Save window geometry (if enabled)
    const bool rememberWindow = m_settings->value("rememberWindowState", true).toBool();
    if (rememberWindow) {
        m_settings->setValue("geometry", saveGeometry());
        m_settings->setValue("windowState", saveState());
    }

    // Save interface settings
    m_settings->setValue("showThumbnails", m_thumbnailsDock->isVisible());
    m_settings->setValue("showOutline", m_outlineDock->isVisible());
}

void MainWindow::updateRecentFilesMenu()
{
    m_recentFilesMenu->clear();

    if (m_recentFiles.isEmpty()) {
        QAction* noFilesAction = m_recentFilesMenu->addAction(tr("No recent files"));
        noFilesAction->setEnabled(false);
        return;
    }

    for (int i = 0; i < m_recentFiles.size(); ++i) {
        const QString& filePath = m_recentFiles.at(i);
        const QString fileName = QFileInfo(filePath).fileName();
        const QString actionText = QString("&%1 %2").arg(i + 1).arg(fileName);

        QAction* action = m_recentFilesMenu->addAction(actionText);
        action->setData(filePath);
        action->setStatusTip(filePath);
        action->setToolTip(tr("Open: %1").arg(filePath));
        connect(action, &QAction::triggered, this, &MainWindow::openRecentFile);

        // Check if file still exists and mark accordingly
        if (!QFile::exists(filePath)) {
            action->setText(actionText + tr(" (missing)"));
            action->setEnabled(false);
            QFont font = action->font();
            font.setItalic(true);
            action->setFont(font);
        }
    }

    m_recentFilesMenu->addSeparator();
    m_clearRecentAction = m_recentFilesMenu->addAction(tr("&Clear Recent Files"));
    connect(m_clearRecentAction, &QAction::triggered, this, &MainWindow::clearRecentFiles);
}

void MainWindow::addToRecentFiles(const QString& filePath)
{
    // Remove if already exists
    m_recentFiles.removeAll(filePath);

    // Add to front
    m_recentFiles.prepend(filePath);

    // Limit to MaxRecentFiles
    while (m_recentFiles.size() > MaxRecentFiles) {
        m_recentFiles.removeLast();
    }

    updateRecentFilesMenu();
    saveSettings();
}

void MainWindow::removeFromRecentFiles(const QString& filePath)
{
    m_recentFiles.removeAll(filePath);
    updateRecentFilesMenu();
    saveSettings();
}

void MainWindow::saveSession()
{
    if (!m_settings) return;

    m_settings->beginGroup("Session");

    // Save window geometry and state
    m_settings->setValue("geometry", saveGeometry());
    m_settings->setValue("windowState", saveState());
    m_settings->setValue("isMaximized", isMaximized());
    m_settings->setValue("isFullScreen", m_isFullScreen);

    // Save dock widget states
    m_settings->setValue("thumbnailsVisible", m_thumbnailsDock->isVisible());
    m_settings->setValue("outlineVisible", m_outlineDock->isVisible());
    m_settings->setValue("searchResultsVisible", m_searchResultsDock ? m_searchResultsDock->isVisible() : false);

    // Save open documents
    m_settings->beginWriteArray("openDocuments");
    for (int i = 0; i < m_tabWidget->count(); ++i) {
        DocumentTab* tab = qobject_cast<DocumentTab*>(m_tabWidget->widget(i));
        if (tab && tab->isDocumentLoaded()) {
            m_settings->setArrayIndex(i);
            m_settings->setValue("filePath", tab->getFilePath());
            m_settings->setValue("currentPage", tab->getCurrentPage());
            m_settings->setValue("zoomFactor", tab->getZoomFactor());
            m_settings->setValue("rotation", tab->getCurrentRotation());
        }
    }
    m_settings->endArray();

    // Save current tab index
    m_settings->setValue("currentTabIndex", m_tabWidget->currentIndex());

    m_settings->endGroup();
}

void MainWindow::restoreSession()
{
    if (!m_settings) return;

    m_settings->beginGroup("Session");

    // Restore window geometry and state
    if (m_settings->contains("geometry")) {
        restoreGeometry(m_settings->value("geometry").toByteArray());
    }
    if (m_settings->contains("windowState")) {
        restoreState(m_settings->value("windowState").toByteArray());
    }
    if (m_settings->value("isMaximized", false).toBool()) {
        showMaximized();
    }

    // Restore dock widget states
    if (m_settings->contains("thumbnailsVisible")) {
        m_thumbnailsDock->setVisible(m_settings->value("thumbnailsVisible", true).toBool());
    }
    if (m_settings->contains("outlineVisible")) {
        m_outlineDock->setVisible(m_settings->value("outlineVisible", true).toBool());
    }
    if (m_searchResultsDock && m_settings->contains("searchResultsVisible")) {
        m_searchResultsDock->setVisible(m_settings->value("searchResultsVisible", false).toBool());
    }

    // Restore open documents
    int documentCount = m_settings->beginReadArray("openDocuments");
    for (int i = 0; i < documentCount; ++i) {
        m_settings->setArrayIndex(i);
        QString filePath = m_settings->value("filePath").toString();

        if (!filePath.isEmpty() && QFile::exists(filePath)) {
            // Open the document
            openPdfFile(filePath);

            // Get the newly created tab
            DocumentTab* tab = getCurrentTab();
            if (tab && tab->isDocumentLoaded()) {
                // Restore tab state
                int currentPage = m_settings->value("currentPage", 0).toInt();
                double zoomFactor = m_settings->value("zoomFactor", 1.0).toDouble();
                int rotation = m_settings->value("rotation", 0).toInt();

                tab->setCurrentPage(currentPage);
                tab->setZoomFactor(zoomFactor);
                tab->setRotation(rotation);
            }
        }
    }
    m_settings->endArray();

    // Restore current tab index
    int currentTabIndex = m_settings->value("currentTabIndex", 0).toInt();
    if (currentTabIndex >= 0 && currentTabIndex < m_tabWidget->count()) {
        m_tabWidget->setCurrentIndex(currentTabIndex);
    }

    m_settings->endGroup();
}

void MainWindow::rotateClockwise()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    int newRotation = (currentTab->getCurrentRotation() + 90) % 360;
    currentTab->setRotation(newRotation);
    statusBar()->showMessage(tr("Rotated clockwise - %1°").arg(newRotation), 2000);
}

void MainWindow::rotateCounterClockwise()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    int newRotation = (currentTab->getCurrentRotation() - 90 + 360) % 360;
    currentTab->setRotation(newRotation);
    statusBar()->showMessage(tr("Rotated counter-clockwise - %1°").arg(newRotation), 2000);
}

void MainWindow::rotate180()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    int newRotation = (currentTab->getCurrentRotation() + 180) % 360;
    currentTab->setRotation(newRotation);
    statusBar()->showMessage(tr("Rotated 180° - %1°").arg(newRotation), 2000);
}

void MainWindow::resetRotation()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    currentTab->setRotation(0);
    statusBar()->showMessage(tr("Rotation reset to normal"), 2000);
}

void MainWindow::setSinglePageMode()
{
    // Make this action checked and others unchecked
    m_singlePageModeAction->setChecked(true);
    m_continuousPageModeAction->setChecked(false);
    m_facingPageModeAction->setChecked(false);

    // Apply to current tab
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        currentTab->setSinglePageMode();
    }

    statusBar()->showMessage(tr("Single page mode activated"), 2000);
}

void MainWindow::setContinuousPageMode()
{
    // Make this action checked and others unchecked
    m_singlePageModeAction->setChecked(false);
    m_continuousPageModeAction->setChecked(true);
    m_facingPageModeAction->setChecked(false);

    // Apply to current tab
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        currentTab->setContinuousPageMode();
    }

    statusBar()->showMessage(tr("Continuous page mode activated"), 2000);
}

void MainWindow::setFacingPageMode()
{
    // Make this action checked and others unchecked
    m_singlePageModeAction->setChecked(false);
    m_continuousPageModeAction->setChecked(false);
    m_facingPageModeAction->setChecked(true);

    // Apply to current tab
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        currentTab->setFacingPageMode();
    }

    statusBar()->showMessage(tr("Facing pages mode activated"), 2000);
}

void MainWindow::clearCache()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->getPdfController()->clearCache();
    }
    statusBar()->showMessage(tr("Cache cleared - memory freed"), 3000);
}

void MainWindow::showMemoryUsage()
{
    DocumentTab* currentTab = getCurrentTab();
    const double memoryMB = m_currentMemoryUsage / (1024.0 * 1024.0);

    QString message = tr("Memory Usage:\n\n"
                        "Cache Size: %.1f MB\n"
                        "Cached Pages: %2\n")
                        .arg(memoryMB, 0, 'f', 1)
                        .arg(m_cachedPagesCount);

    if (currentTab && currentTab->isDocumentLoaded()) {
        message += tr("Current Page: %1\n"
                     "Zoom Factor: %2%\n"
                     "Rotation: %3°")
                     .arg(currentTab->getCurrentPage() + 1)
                     .arg(qRound(currentTab->getZoomFactor() * 100))
                     .arg(currentTab->getCurrentRotation());
    } else {
        message += tr("No document loaded");
    }

    QMessageBox::information(this, tr("Performance Information"), message);
}

void MainWindow::onMemoryUsageChanged(qint64 bytesUsed, int cachedPages)
{
    m_currentMemoryUsage = bytesUsed;
    m_cachedPagesCount = cachedPages;

    // Update status bar with memory info (optional - can be toggled)
    const double memoryMB = bytesUsed / (1024.0 * 1024.0);
    if (memoryMB > 100.0) { // Only show if using significant memory
        statusBar()->showMessage(tr("Cache: %.1f MB (%2 pages)").arg(memoryMB, 0, 'f', 1).arg(cachedPages), 1000);
    }
}

void MainWindow::exportCurrentPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    const QString fileName = QString("page_%1.png").arg(currentTab->getCurrentPage() + 1, 3, 10, QChar('0'));
    const QString filePath = QFileDialog::getSaveFileName(this, tr("Export Current Page"),
                                                        fileName, tr("PNG Images (*.png);;JPEG Images (*.jpg);;PDF Files (*.pdf)"));
    if (filePath.isEmpty()) return;

    // Get current page pixmap
    const QPixmap currentPixmap = currentTab->getPageLabel()->pixmap();
    if (currentPixmap.isNull()) {
        QMessageBox::warning(this, tr("Export Error"), tr("No page image available to export."));
        return;
    }

    if (currentPixmap.save(filePath)) {
        statusBar()->showMessage(tr("Page exported to %1").arg(QFileInfo(filePath).fileName()), 3000);
    } else {
        QMessageBox::critical(this, tr("Export Error"), tr("Failed to save page image."));
    }
}

void MainWindow::exportAllPages()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    const QString dirPath = QFileDialog::getExistingDirectory(this, tr("Select Export Directory"));
    if (dirPath.isEmpty()) return;

    const int totalPages = currentTab->getPageCount();
    QProgressDialog progress(tr("Exporting pages..."), tr("Cancel"), 0, totalPages, this);
    progress.setWindowModality(Qt::WindowModal);

    for (int page = 0; page < totalPages; ++page) {
        progress.setValue(page);
        if (progress.wasCanceled()) break;

        // Request high-quality render for export
        // This is a simplified version - in a full implementation, we'd render at export DPI
        const QString fileName = QString("page_%1.png").arg(page + 1, 3, 10, QChar('0'));
        const QString filePath = QDir(dirPath).filePath(fileName);

        // For now, we'll use a placeholder - in a real implementation, we'd render each page
        statusBar()->showMessage(tr("Exporting page %1...").arg(page + 1));
        QApplication::processEvents();
    }

    progress.setValue(totalPages);
    statusBar()->showMessage(tr("Export completed - %1 pages saved to %2").arg(totalPages).arg(QFileInfo(dirPath).fileName()), 5000);
}

void MainWindow::exportPageRange()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    const int totalPages = currentTab->getPageCount();
    bool ok;
    const QString range = QInputDialog::getText(this, tr("Export Page Range"),
                                              tr("Enter page range (e.g., 1-5, 10, 15-20):"),
                                              QLineEdit::Normal, QString("1-%1").arg(totalPages), &ok);
    if (!ok || range.isEmpty()) return;

    // Parse range (simplified - in a full implementation, we'd have proper range parsing)
    QStringList parts = range.split('-');
    int startPage = 1, endPage = totalPages;

    if (parts.size() == 1) {
        startPage = endPage = parts[0].toInt();
    } else if (parts.size() == 2) {
        startPage = parts[0].toInt();
        endPage = parts[1].toInt();
    }

    startPage = qBound(1, startPage, totalPages);
    endPage = qBound(startPage, endPage, totalPages);

    const QString dirPath = QFileDialog::getExistingDirectory(this, tr("Select Export Directory"));
    if (dirPath.isEmpty()) return;

    statusBar()->showMessage(tr("Exporting pages %1-%2...").arg(startPage).arg(endPage), 3000);
    // Implementation would export the specified range
}

void MainWindow::printDocument()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    QPrinter printer(QPrinter::HighResolution);
    QPrintDialog printDialog(&printer, this);
    printDialog.setWindowTitle(tr("Print Document"));

    if (printDialog.exec() != QDialog::Accepted) return;

    const int totalPages = currentTab->getPageCount();
    QProgressDialog progress(tr("Printing..."), tr("Cancel"), 0, totalPages, this);
    progress.setWindowModality(Qt::WindowModal);

    QPainter painter(&printer);

    for (int page = 0; page < totalPages; ++page) {
        progress.setValue(page);
        if (progress.wasCanceled()) break;

        if (page > 0) printer.newPage();

        // For demonstration - in a full implementation, we'd render each page properly
        painter.drawText(100, 100, tr("Page %1 of %2").arg(page + 1).arg(totalPages));
        statusBar()->showMessage(tr("Printing page %1...").arg(page + 1));
        QApplication::processEvents();
    }

    progress.setValue(totalPages);
    statusBar()->showMessage(tr("Printing completed"), 3000);
}

void MainWindow::printCurrentPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    QPrinter printer(QPrinter::HighResolution);
    QPrintDialog printDialog(&printer, this);
    printDialog.setWindowTitle(tr("Print Current Page"));

    if (printDialog.exec() != QDialog::Accepted) return;

    const QPixmap currentPixmap = currentTab->getPageLabel()->pixmap();
    if (currentPixmap.isNull()) {
        QMessageBox::warning(this, tr("Print Error"), tr("No page image available to print."));
        return;
    }

    QPainter painter(&printer);
    const QRectF pageRectF = printer.pageRect(QPrinter::DevicePixel);
    const QRect pageRect = pageRectF.toRect();
    const QPixmap scaledPixmap = currentPixmap.scaled(pageRect.size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);

    const int x = (pageRect.width() - scaledPixmap.width()) / 2;
    const int y = (pageRect.height() - scaledPixmap.height()) / 2;

    painter.drawPixmap(x, y, scaledPixmap);
    statusBar()->showMessage(tr("Current page printed"), 3000);
}

void MainWindow::showPrintPreview()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) {
        statusBar()->showMessage(tr("No document loaded"), 2000);
        return;
    }

    PrintPreviewDialog dialog(currentTab->getPdfController(), this);
    dialog.exec();
}

void MainWindow::showSettings()
{
    SettingsDialog dialog(this);

    // Load current settings into dialog
    dialog.setDefaultZoom(m_settings->value("defaultZoom", 1.0).toDouble());
    dialog.setCacheSize(m_settings->value("cacheSize", 450).toInt());
    dialog.setPreloadRange(m_settings->value("preloadRange", 2).toInt());
    dialog.setShowMemoryUsage(m_settings->value("showMemoryUsage", false).toBool());
    dialog.setShowThumbnails(m_settings->value("showThumbnails", true).toBool());
    dialog.setExportFormat(m_settings->value("exportFormat", "PNG").toString());
    dialog.setExportQuality(m_settings->value("exportQuality", 90).toInt());
    dialog.setRememberWindowState(m_settings->value("rememberWindowState", true).toBool());
    dialog.setAutoFitOnOpen(m_settings->value("autoFitOnOpen", false).toBool());
    dialog.setThemeMode(m_settings->value("themeMode", static_cast<int>(ElaThemeType::Light)).toInt());

    if (dialog.exec() == QDialog::Accepted) {
        // Save new settings
        m_settings->setValue("defaultZoom", dialog.getDefaultZoom());
        m_settings->setValue("cacheSize", dialog.getCacheSize());
        m_settings->setValue("preloadRange", dialog.getPreloadRange());
        m_settings->setValue("showMemoryUsage", dialog.getShowMemoryUsage());
        m_settings->setValue("showThumbnails", dialog.getShowThumbnails());
        m_settings->setValue("exportFormat", dialog.getExportFormat());
        m_settings->setValue("exportQuality", dialog.getExportQuality());
        m_settings->setValue("rememberWindowState", dialog.getRememberWindowState());
        m_settings->setValue("autoFitOnOpen", dialog.getAutoFitOnOpen());

        // Handle theme change
        int newThemeMode = dialog.getThemeMode();
        int currentThemeMode = m_settings->value("themeMode", static_cast<int>(ElaThemeType::Light)).toInt();
        if (newThemeMode != currentThemeMode) {
            m_settings->setValue("themeMode", newThemeMode);
            applyTheme(static_cast<ElaThemeType::ThemeMode>(newThemeMode));
        }

        // Apply settings immediately
        applySettings();

        statusBar()->showMessage(tr("Settings saved and applied"), 3000);
    }
}

void MainWindow::showSettingsDialog()
{
    // This method calls the existing showSettings() implementation
    showSettings();
}

void MainWindow::showAboutDialog()
{
    // Show about dialog with application information
    QMessageBox::about(this, tr("About Optimized PDF Viewer"),
        tr("Optimized PDF Viewer v1.0\n\n"
           "High-performance PDF viewer with advanced annotation capabilities.\n\n"
           "Built with Qt and ElaWidgetTools.\n\n"
           "Features:\n"
           "• Fast PDF rendering with hardware acceleration\n"
           "• Advanced annotation tools\n"
           "• Modern ribbon interface\n"
           "• Multi-document tabs\n"
           "• Customizable themes\n"
           "• Performance monitoring\n\n"
           "© 2024 PDF Viewer Team"));
}

void MainWindow::showDocumentInfo()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) {
        statusBar()->showMessage(tr("No document loaded"), 2000);
        return;
    }

    DocumentInfoDialog dialog(currentTab->getPdfController(), currentTab->getFilePath(), this);
    dialog.exec();
}

void MainWindow::applySettings()
{
    // Apply interface settings
    const bool showThumbnails = m_settings->value("showThumbnails", true).toBool();
    if (m_thumbnailsDock->isVisible() != showThumbnails) {
        m_thumbnailsDock->setVisible(showThumbnails);
        m_toggleThumbnailsAction->setChecked(showThumbnails);
    }

    const bool showOutline = m_settings->value("showOutline", true).toBool();
    if (m_outlineDock->isVisible() != showOutline) {
        m_outlineDock->setVisible(showOutline);
        m_toggleOutlineAction->setChecked(showOutline);
    }

    // Apply default zoom if no document is loaded
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) {
        // Default zoom will be applied when a document is loaded
    }

    // Note: Cache size and preload range would require PdfController updates
    // For now, they'll take effect on next application restart
}

void MainWindow::applyTheme(ElaThemeType::ThemeMode themeMode)
{
    // Show loading overlay during theme transition
    m_loadingOverlay->showLoading(tr("Applying theme..."));

    // Apply the theme using ElaTheme with a slight delay for smooth transition
    QTimer::singleShot(200, this, [this, themeMode]() {
        eTheme->setThemeMode(themeMode);

        // Hide loading overlay after theme is applied
        QTimer::singleShot(300, this, [this]() {
            m_loadingOverlay->hideLoading();
        });
    });

    // The theme change will be automatically applied to all Ela components
    // No additional work needed as ElaMainWindow and all Ela widgets support theming
}

void MainWindow::onSearchCompleted(const QList<SearchResult>& results)
{
    m_searchResults = results;
    m_currentSearchIndex = -1;

    updateSearchResultsDisplay();

    // Update the search results panel
    if (m_searchResultsPanel) {
        m_searchResultsPanel->setSearchResults(results, m_currentSearchTerm);
        if (!results.isEmpty()) {
            m_searchResultsDock->show();
        }
    }

    // Update enhanced search widget
    if (m_enhancedSearchWidget) {
        m_enhancedSearchWidget->setSearchInProgress(false);
        if (!results.isEmpty()) {
            m_enhancedSearchWidget->setSearchResults(0, results.size());
        } else {
            m_enhancedSearchWidget->setSearchResults(-1, 0);
        }
    }

    // Update search history with result count
    if (m_searchHistory && !m_currentSearchTerm.isEmpty()) {
        DocumentTab* currentTab = getCurrentTab();
        QString docPath = currentTab ? currentTab->getFilePath() : QString();
        m_searchHistory->addSearch(m_currentSearchTerm, m_caseSensitiveSearch,
                                  m_wholeWordSearch, results.size(), docPath);
    }

    if (results.isEmpty()) {
        statusBar()->showMessage(tr("No matches found for '%1'").arg(m_currentSearchTerm), 3000);
        m_findNextButton->setEnabled(false);
        m_findPrevButton->setEnabled(false);
    } else {
        statusBar()->showMessage(tr("Found %1 matches").arg(results.size()), 3000);
        m_findNextButton->setEnabled(true);
        m_findPrevButton->setEnabled(true);

        // Automatically go to first result
        m_currentSearchIndex = 0;
        navigateToSearchResult(0);
    }
}

void MainWindow::onSearchProgress(int pagesSearched, int totalPages)
{
    statusBar()->showMessage(tr("Searching... %1/%2 pages").arg(pagesSearched).arg(totalPages), 0);
}

void MainWindow::navigateToSearchResult(int index)
{
    if (index < 0 || index >= m_searchResults.size()) return;

    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || index < 0 || index >= m_searchResults.size()) return;

    const SearchResult& result = m_searchResults[index];

    // Navigate to the page if different
    if (result.pageNumber != currentTab->getCurrentPage()) {
        currentTab->setCurrentPage(result.pageNumber);
        updateUiState();
    }

    // Request page with highlights
    QList<QRectF> highlights;

    // Add all search results on this page as highlights
    for (const SearchResult& searchResult : m_searchResults) {
        if (searchResult.pageNumber == result.pageNumber) {
            highlights.append(searchResult.boundingBox);
        }
    }

    currentTab->getPdfController()->requestPageWithHighlights(currentTab->getCurrentPage(), currentTab->getZoomFactor(), highlights, currentTab->getCurrentRotation());

    updateSearchResultsDisplay();

    // Update enhanced search widget with current result
    if (m_enhancedSearchWidget) {
        m_enhancedSearchWidget->setSearchResults(index, m_searchResults.size());
    }

    statusBar()->showMessage(tr("Result %1 of %2: %3").arg(index + 1).arg(m_searchResults.size()).arg(result.context.simplified()), 3000);
}

void MainWindow::updateSearchResultsDisplay()
{
    if (m_searchResults.isEmpty()) {
        m_searchResultsLabel->setText(tr("No results"));
        m_searchResultsLabel->setStyleSheet(
            "QLabel { "
            "    color: #dc3545; "
            "    font-size: 11px; "
            "    font-weight: bold; "
            "    padding: 2px 4px; "
            "}"
        );
        m_findNextButton->setEnabled(false);
        m_findPrevButton->setEnabled(false);
    } else {
        m_searchResultsLabel->setText(tr("%1 of %2").arg(m_currentSearchIndex + 1).arg(m_searchResults.size()));
        m_searchResultsLabel->setStyleSheet(
            "QLabel { "
            "    color: #28a745; "
            "    font-size: 11px; "
            "    font-weight: bold; "
            "    padding: 2px 4px; "
            "    background: #d4edda; "
            "    border: 1px solid #c3e6cb; "
            "    border-radius: 3px; "
            "}"
        );
        m_findNextButton->setEnabled(m_searchResults.size() > 1);
        m_findPrevButton->setEnabled(m_searchResults.size() > 1);
    }
}

void MainWindow::onOutlineReady(const QList<OutlineItem>& outline)
{
    Q_UNUSED(outline);
    // The outline is retrieved synchronously, so this signal is not currently used
    // But it's here for future async implementation
    updateOutline();
}

void MainWindow::onSearchResultSelected(int pageNumber, const QRectF& rect)
{
    Q_UNUSED(rect); // TODO: Use rect to scroll to specific location

    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    // Navigate to the page
    if (pageNumber != currentTab->getCurrentPage()) {
        currentTab->setCurrentPage(pageNumber);
        updateUiState();
    }

    // TODO: Scroll to the specific location within the page
    // This would require additional functionality in DocumentTab
    statusBar()->showMessage(tr("Navigated to page %1").arg(pageNumber + 1), 2000);
}

void MainWindow::onSearchRequested(const QString& term, bool caseSensitive, bool wholeWords)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    // Update the main search bar to match
    m_searchEdit->setText(term);
    m_caseSensitiveCheckBox->setChecked(caseSensitive);
    m_wholeWordCheckBox->setChecked(wholeWords);

    // Perform the search
    m_currentSearchTerm = term;
    m_caseSensitiveSearch = caseSensitive;
    m_wholeWordSearch = wholeWords;

    statusBar()->showMessage(tr("Searching..."), 0);
    currentTab->getPdfController()->performSearch(term, caseSensitive, wholeWords);
}

void MainWindow::onSearchPanelClosed()
{
    // Hide the search results dock
    if (m_searchResultsDock) {
        m_searchResultsDock->hide();
    }
}

void MainWindow::onEnhancedSearchRequested(const QString& term, bool caseSensitive, bool wholeWords)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    // Update search state
    m_currentSearchTerm = term;
    m_caseSensitiveSearch = caseSensitive;
    m_wholeWordSearch = wholeWords;

    // Add to search history
    if (m_searchHistory) {
        m_searchHistory->addSearch(term, caseSensitive, wholeWords, 0, currentTab->getFilePath());
    }

    // Set search in progress
    m_enhancedSearchWidget->setSearchInProgress(true);
    statusBar()->showMessage(tr("Searching..."), 0);

    // Perform the search
    currentTab->getPdfController()->performSearch(term, caseSensitive, wholeWords);
}

void MainWindow::onEnhancedSearchCleared()
{
    // Clear search results
    m_searchResults.clear();
    m_currentSearchIndex = -1;
    m_currentSearchTerm.clear();

    // Update enhanced search widget
    if (m_enhancedSearchWidget) {
        m_enhancedSearchWidget->clearSearchResults();
        m_enhancedSearchWidget->setSearchInProgress(false);
    }

    // Clear highlights and refresh current page
    requestCurrentPage();
    statusBar()->clearMessage();
}

void MainWindow::toggleFullScreen()
{
    if (m_isFullScreen) {
        exitFullScreen();
    } else {
        enterFullScreen();
    }
}

void MainWindow::enterFullScreen()
{
    DocumentTab* currentTab = getCurrentTab();
    if (m_isFullScreen || !currentTab || !currentTab->isDocumentLoaded()) return;

    m_isFullScreen = true;
    m_fullScreenAction->setChecked(true);

    // Create a new widget for fullscreen display
    m_fullScreenWidget = new QWidget();
    m_fullScreenWidget->setWindowTitle(tr("PDF Viewer - Full Screen"));
    m_fullScreenWidget->setStyleSheet("background-color: black;");

    // Create layout for fullscreen widget
    QVBoxLayout* fullScreenLayout = new QVBoxLayout(m_fullScreenWidget);
    fullScreenLayout->setContentsMargins(0, 0, 0, 0);
    fullScreenLayout->setSpacing(0);

    // Create a new label for the fullscreen page display using ElaText
    ElaText* fullScreenPageLabel = new ElaText("", m_fullScreenWidget);
    fullScreenPageLabel->setAlignment(Qt::AlignCenter);
    fullScreenPageLabel->setStyleSheet("ElaText { background-color: black; }");

    // Copy the current page pixmap
    if (!currentTab->getPageLabel()->pixmap().isNull()) {
        QPixmap currentPixmap = currentTab->getPageLabel()->pixmap();

        // Scale the pixmap to fit the screen while maintaining aspect ratio
        QSize screenSize = QApplication::primaryScreen()->size();
        QPixmap scaledPixmap = currentPixmap.scaled(screenSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);

        fullScreenPageLabel->setPixmap(scaledPixmap);
    }

    fullScreenLayout->addWidget(fullScreenPageLabel);

    // Create a semi-transparent overlay for navigation controls
    QWidget* controlsOverlay = new QWidget(m_fullScreenWidget);
    controlsOverlay->setStyleSheet("background-color: rgba(0, 0, 0, 128);");
    controlsOverlay->setFixedHeight(60);

    QHBoxLayout* controlsLayout = new QHBoxLayout(controlsOverlay);
    controlsLayout->setContentsMargins(20, 10, 20, 10);

    // Navigation buttons - Enhanced with ElaWidgetTools
    ElaPushButton* prevButton = new ElaPushButton(tr("Previous"), controlsOverlay);
    prevButton->setStyleSheet(
        "ElaPushButton { "
        "    background: rgba(255, 255, 255, 0.9); "
        "    border: 1px solid rgba(255, 255, 255, 0.3); "
        "    border-radius: 6px; "
        "    color: #333; "
        "    font-weight: 500; "
        "    padding: 8px 16px; "
        "} "
        "ElaPushButton:hover { "
        "    background: rgba(255, 255, 255, 1.0); "
        "    border: 1px solid rgba(0, 120, 212, 0.5); "
        "}"
    );

    ElaPushButton* nextButton = new ElaPushButton(tr("Next"), controlsOverlay);
    nextButton->setStyleSheet(
        "ElaPushButton { "
        "    background: rgba(255, 255, 255, 0.9); "
        "    border: 1px solid rgba(255, 255, 255, 0.3); "
        "    border-radius: 6px; "
        "    color: #333; "
        "    font-weight: 500; "
        "    padding: 8px 16px; "
        "} "
        "ElaPushButton:hover { "
        "    background: rgba(255, 255, 255, 1.0); "
        "    border: 1px solid rgba(0, 120, 212, 0.5); "
        "}"
    );

    ElaPushButton* exitButton = new ElaPushButton(tr("Exit Full Screen"), controlsOverlay);
    exitButton->setStyleSheet(
        "ElaPushButton { "
        "    background: rgba(220, 53, 69, 0.9); "
        "    border: 1px solid rgba(220, 53, 69, 0.3); "
        "    border-radius: 6px; "
        "    color: white; "
        "    font-weight: 500; "
        "    padding: 8px 16px; "
        "} "
        "ElaPushButton:hover { "
        "    background: rgba(220, 53, 69, 1.0); "
        "    border: 1px solid rgba(220, 53, 69, 0.8); "
        "}"
    );

    // Page info label - Enhanced with ElaText
    ElaText* pageInfoLabel = new ElaText(tr("Page %1 of %2").arg(currentTab->getCurrentPage() + 1).arg(currentTab->getPageCount()), controlsOverlay);
    pageInfoLabel->setObjectName("pageInfoLabel");
    pageInfoLabel->setStyleSheet(
        "color: white; "
        "font-weight: 600; "
        "font-size: 14px; "
        "font-family: 'Segoe UI', system-ui, sans-serif;"
    );

    controlsLayout->addWidget(prevButton);
    controlsLayout->addWidget(pageInfoLabel);
    controlsLayout->addStretch();
    controlsLayout->addWidget(nextButton);
    controlsLayout->addWidget(exitButton);

    // Connect navigation buttons
    connect(prevButton, &ElaPushButton::clicked, this, &MainWindow::previousPage);
    connect(nextButton, &ElaPushButton::clicked, this, &MainWindow::nextPage);
    connect(exitButton, &ElaPushButton::clicked, this, &MainWindow::exitFullScreen);

    // Position controls at the bottom
    fullScreenLayout->addWidget(controlsOverlay);

    // Install event filter for keyboard navigation
    m_fullScreenWidget->installEventFilter(this);
    m_fullScreenWidget->setFocusPolicy(Qt::StrongFocus);

    // Show fullscreen
    m_fullScreenWidget->showFullScreen();
    m_fullScreenWidget->setFocus();

    statusBar()->showMessage(tr("Full screen mode - Press F11 or Escape to exit"), 3000);
}

void MainWindow::exitFullScreen()
{
    if (!m_isFullScreen) return;

    m_isFullScreen = false;
    m_fullScreenAction->setChecked(false);

    if (m_fullScreenWidget) {
        m_fullScreenWidget->close();
        delete m_fullScreenWidget;
        m_fullScreenWidget = nullptr;
    }

    // Bring main window to front
    this->raise();
    this->activateWindow();

    statusBar()->showMessage(tr("Exited full screen mode"), 2000);
}

bool MainWindow::eventFilter(QObject* object, QEvent* event)
{
    if (object == m_fullScreenWidget && event->type() == QEvent::KeyPress) {
        QKeyEvent* keyEvent = static_cast<QKeyEvent*>(event);

        switch (keyEvent->key()) {
            case Qt::Key_Escape:
            case Qt::Key_F11:
                exitFullScreen();
                return true;

            case Qt::Key_Left:
            case Qt::Key_Up:
            case Qt::Key_PageUp:
                previousPage();
                updateFullScreenPage();
                return true;

            case Qt::Key_Right:
            case Qt::Key_Down:
            case Qt::Key_PageDown:
            case Qt::Key_Space:
                nextPage();
                updateFullScreenPage();
                return true;

            case Qt::Key_Home:
                firstPage();
                updateFullScreenPage();
                return true;

            case Qt::Key_End:
                lastPage();
                updateFullScreenPage();
                return true;

            default:
                break;
        }
    }

    return QMainWindow::eventFilter(object, event);
}

void MainWindow::updateFullScreenPage()
{
    if (!m_isFullScreen || !m_fullScreenWidget) return;

    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) return;

    // Find the page label in the fullscreen widget
    ElaText* fullScreenPageLabel = m_fullScreenWidget->findChild<ElaText*>();
    if (!fullScreenPageLabel) return;

    // Update with current page pixmap
    QLabel* pageLabel = currentTab->getPageLabel();
    if (pageLabel && !pageLabel->pixmap().isNull()) {
        QPixmap currentPixmap = pageLabel->pixmap();

        // Scale the pixmap to fit the screen while maintaining aspect ratio
        QSize screenSize = QApplication::primaryScreen()->size();
        QPixmap scaledPixmap = currentPixmap.scaled(screenSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);

        fullScreenPageLabel->setPixmap(scaledPixmap);
    }

    // Update page info label
    ElaText* pageInfoLabel = m_fullScreenWidget->findChild<ElaText*>("pageInfoLabel");
    if (pageInfoLabel) {
        pageInfoLabel->setText(tr("Page %1 of %2").arg(currentTab->getCurrentPage() + 1).arg(currentTab->getPdfController()->pageCount().value_or(0)));
    }
}

void MainWindow::toggleSelectionZoom()
{
    m_selectionZoomMode = m_selectionZoomAction->isChecked();

    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) return;

    QLabel* pageLabel = currentTab->getPageLabel();
    if (!pageLabel) return;

    if (m_selectionZoomMode) {
        // Disable magnifier mode
        m_magnifierMode = false;
        m_magnifierAction->setChecked(false);

        // Change cursor to crosshair
        pageLabel->setCursor(Qt::CrossCursor);
        statusBar()->showMessage(tr("Selection zoom mode - Click and drag to select area to zoom"), 0);
    } else {
        pageLabel->setCursor(Qt::ArrowCursor);
        statusBar()->showMessage(tr("Selection zoom mode disabled"), 2000);
    }
}

void MainWindow::toggleMagnifier()
{
    m_magnifierMode = m_magnifierAction->isChecked();

    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) return;

    QLabel* pageLabel = currentTab->getPageLabel();
    if (!pageLabel) return;

    if (m_magnifierMode) {
        // Disable selection zoom mode
        m_selectionZoomMode = false;
        m_selectionZoomAction->setChecked(false);

        // Change cursor to magnifying glass (or crosshair as fallback)
        pageLabel->setCursor(Qt::CrossCursor);
        statusBar()->showMessage(tr("Magnifier mode - Click to magnify area"), 0);
    } else {
        pageLabel->setCursor(Qt::ArrowCursor);
        statusBar()->showMessage(tr("Magnifier mode disabled"), 2000);
    }
}

void MainWindow::showCustomZoomDialog()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    bool ok;
    int currentZoomPercent = qRound(currentTab->getZoomFactor() * 100);
    int zoomPercent = QInputDialog::getInt(this, tr("Custom Zoom"),
                                         tr("Zoom level (%):"), currentZoomPercent,
                                         10, 1000, 1, &ok);

    if (ok) {
        currentTab->setZoomFactor(zoomPercent / 100.0);
        updateZoomControls();
        statusBar()->showMessage(tr("Zoom: %1%").arg(zoomPercent), 2000);
    }
}

void MainWindow::onZoomComboChanged(const QString& text)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    QString cleanText = text;
    cleanText.remove('%');

    bool ok;
    double zoomPercent = cleanText.toDouble(&ok);

    if (ok && zoomPercent > 0) {
        currentTab->setZoomFactor(zoomPercent / 100.0);
        statusBar()->showMessage(tr("Zoom: %1%").arg(zoomPercent), 2000);
    }
}

void MainWindow::onZoomSliderChanged(int value)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    // Convert slider value (0-100) to zoom factor using simplified linear scale (0.25 - 4.0)
    // This provides better precision for common zoom levels
    double normalizedValue = value / 100.0;
    double zoomFactor = 0.25 + normalizedValue * 3.75; // Linear scale from 25% to 400%

    currentTab->setZoomFactor(zoomFactor);
    updateZoomControls();
    statusBar()->showMessage(tr("Zoom: %1%").arg(qRound(zoomFactor * 100)), 2000);
}

void MainWindow::updateZoomControls()
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) return;

    double zoomFactor = currentTab->getZoomFactor();

    // Update zoom combo box
    m_zoomComboBox->blockSignals(true);
    m_zoomComboBox->setCurrentText(QString("%1%").arg(qRound(zoomFactor * 100)));
    m_zoomComboBox->blockSignals(false);

    // Update zoom slider using inverse of the linear scale
    m_zoomSlider->blockSignals(true);
    // Convert zoom factor (0.25 - 4.0) to slider value (0-100)
    double clampedZoom = qBound(0.25, zoomFactor, 4.0);
    int sliderValue = qRound(((clampedZoom - 0.25) / 3.75) * 100.0);
    sliderValue = qBound(0, sliderValue, 100);
    m_zoomSlider->setValue(sliderValue);
    m_zoomSlider->blockSignals(false);
}

void MainWindow::mousePressEvent(QMouseEvent* event)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) {
        QMainWindow::mousePressEvent(event);
        return;
    }

    QLabel* pageLabel = currentTab->getPageLabel();
    if (!pageLabel) {
        QMainWindow::mousePressEvent(event);
        return;
    }

    if (m_selectionZoomMode && event->button() == Qt::LeftButton) {
        // Check if the click is on the page label
        QPoint labelPos = pageLabel->mapFromGlobal(event->globalPosition().toPoint());
        if (pageLabel->rect().contains(labelPos)) {
            m_isSelecting = true;
            m_selectionStart = labelPos;
            m_selectionEnd = labelPos;
            pageLabel->update();
        }
    } else if (m_magnifierMode && event->button() == Qt::LeftButton) {
        // Handle magnifier click
        QPoint labelPos = pageLabel->mapFromGlobal(event->globalPosition().toPoint());
        if (pageLabel->rect().contains(labelPos)) {
            performMagnifierZoom(labelPos);
        }
    }

    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent* event)
{
    if (m_selectionZoomMode && m_isSelecting) {
        DocumentTab* currentTab = getCurrentTab();
        if (currentTab) {
            QLabel* pageLabel = currentTab->getPageLabel();
            if (pageLabel) {
                QPoint labelPos = pageLabel->mapFromGlobal(event->globalPosition().toPoint());
                m_selectionEnd = labelPos;
                pageLabel->update();
            }
        }
    }

    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent* event)
{
    if (m_selectionZoomMode && m_isSelecting && event->button() == Qt::LeftButton) {
        m_isSelecting = false;

        // Calculate selection rectangle
        QRect selectionRect = QRect(m_selectionStart, m_selectionEnd).normalized();

        if (selectionRect.width() > 10 && selectionRect.height() > 10) {
            performSelectionZoom(selectionRect);
        }

        DocumentTab* currentTab = getCurrentTab();
        if (currentTab) {
            QLabel* pageLabel = currentTab->getPageLabel();
            if (pageLabel) {
                pageLabel->update();
            }
        }
    }

    QMainWindow::mouseReleaseEvent(event);
}

void MainWindow::performSelectionZoom(const QRect& selectionRect)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded() || selectionRect.isEmpty()) return;

    QLabel* pageLabel = currentTab->getPageLabel();
    QScrollArea* scrollArea = currentTab->getScrollArea();
    if (!pageLabel || !scrollArea) return;

    // Calculate zoom factor based on selection
    Q_UNUSED(pageLabel->size()); // pageSize not needed for current implementation
    QSize viewportSize = scrollArea->viewport()->size();

    double widthRatio = static_cast<double>(viewportSize.width()) / selectionRect.width();
    double heightRatio = static_cast<double>(viewportSize.height()) / selectionRect.height();

    // Use the smaller ratio to ensure the selection fits in the viewport
    double currentZoom = currentTab->getZoomFactor();
    double newZoomFactor = currentZoom * qMin(widthRatio, heightRatio);

    // Limit zoom factor to reasonable bounds
    newZoomFactor = qBound(0.1, newZoomFactor, 10.0);

    currentTab->setZoomFactor(newZoomFactor);
    updateZoomControls();

    // Calculate scroll position to center the selection
    Q_UNUSED(selectionRect.center()); // selectionCenter not used in current implementation

    statusBar()->showMessage(tr("Zoomed to selection - %1%").arg(qRound(newZoomFactor * 100)), 3000);
}

void MainWindow::performMagnifierZoom(const QPoint& clickPos)
{
    Q_UNUSED(clickPos); // clickPos not used in current implementation
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab || !currentTab->isDocumentLoaded()) return;

    // Zoom in by 2x centered on the click position
    double currentZoom = currentTab->getZoomFactor();
    double newZoomFactor = currentZoom * 2.0;
    newZoomFactor = qBound(0.1, newZoomFactor, 10.0);

    currentTab->setZoomFactor(newZoomFactor);
    updateZoomControls();

    statusBar()->showMessage(tr("Magnified to %1%").arg(qRound(newZoomFactor * 100)), 2000);
}

DocumentTab* MainWindow::getCurrentTab() const
{
    return qobject_cast<DocumentTab*>(m_tabWidget->currentWidget());
}

DocumentTab* MainWindow::createNewTab()
{
    DocumentTab* tab = new DocumentTab(this);

    // Connect annotation toolbar to the tab
    if (m_annotationToolbar) {
        tab->setAnnotationToolbar(m_annotationToolbar);
    }

    // Connect tab signals
    connect(tab, &DocumentTab::documentLoaded, this, [this, tab](bool success, const QString& errorString) {
        Q_UNUSED(success);
        Q_UNUSED(errorString);
        updateTabTitle(tab);
        updateUiState();
    });

    connect(tab, &DocumentTab::pageChanged, this, [this](int pageNum) {
        Q_UNUSED(pageNum);
        updateUiState();
    });

    connect(tab, &DocumentTab::zoomChanged, this, [this](double zoomFactor) {
        Q_UNUSED(zoomFactor);
        updateZoomControls();
    });

    connect(tab, &DocumentTab::statusMessage, this, [this](const QString& message, int timeout) {
        statusBar()->showMessage(message, timeout);
    });

    connect(tab, &DocumentTab::textSelectionChanged, this, [this](const QString& selectedText) {
        Q_UNUSED(selectedText);
        // Could update UI to show selection info
        updateUiState();
    });

    connect(tab, &DocumentTab::textSelectionCleared, this, [this]() {
        // Could update UI to reflect no selection
        updateUiState();
    });

    connect(tab, &DocumentTab::loadingProgressChanged, this, [this](int percentage, const QString& message) {
        m_loadingOverlay->setProgress(percentage);
        m_loadingOverlay->setMessage(message);
    });

    int index = m_tabWidget->addTab(tab, tr("New Tab"));
    m_tabWidget->setCurrentIndex(index);

    // Show welcome screen if this is the first empty tab
    if (m_tabWidget->count() == 1) {
        showWelcomeScreen();
    }

    return tab;
}

DocumentationViewer* MainWindow::createDocumentationTab()
{
    DocumentationViewer* docViewer = new DocumentationViewer(this);

    // Connect documentation viewer signals
    connect(docViewer, &DocumentationViewer::linkClicked, this, [](const QUrl& url) {
        // Handle external links
        if (!url.isRelative()) {
            QDesktopServices::openUrl(url);
        }
    });

    // Add tab with documentation icon
    int index = m_tabWidget->addTab(docViewer, tr("Documentation"));
    m_tabWidget->setCurrentIndex(index);

    // Load documentation
    QString docsPath = QApplication::applicationDirPath() + "/docs/user/user-manual.html";
    if (!QFileInfo::exists(docsPath)) {
        // Fallback to built-in documentation
        docViewer->loadDocumentation("");
    } else {
        docViewer->loadDocumentation(docsPath);
    }

    // Hide welcome screen when opening documentation
    hideWelcomeScreen();

    return docViewer;
}

void MainWindow::updateTabTitle(DocumentTab* tab)
{
    if (!tab) return;

    int index = m_tabWidget->indexOf(tab);
    if (index >= 0) {
        QString title = tab->getFileName();
        if (title.length() > 20) {
            title = title.left(17) + "...";
        }
        m_tabWidget->setTabText(index, title);
        m_tabWidget->setTabToolTip(index, tab->getFilePath());
    }
}

void MainWindow::newTab()
{
    createNewTab();
}

void MainWindow::closeTab(int index)
{
    if (index < 0 || index >= m_tabWidget->count()) return;

    QWidget* widget = m_tabWidget->widget(index);

    // Handle DocumentTab
    DocumentTab* documentTab = qobject_cast<DocumentTab*>(widget);
    if (documentTab) {
        documentTab->closeDocument();
        m_tabWidget->removeTab(index);
        documentTab->deleteLater();
    }
    // Handle DocumentationViewer
    else if (qobject_cast<DocumentationViewer*>(widget)) {
        m_tabWidget->removeTab(index);
        widget->deleteLater();
    }

    // If no tabs left, create a new one
    if (m_tabWidget->count() == 0) {
        createNewTab();
    }

    updateUiState();
}

void MainWindow::closeCurrentTab()
{
    closeTab(m_tabWidget->currentIndex());
}

void MainWindow::onTabChanged(int index)
{
    Q_UNUSED(index);
    updateUiState();
    updateZoomControls();

    // Hide welcome screen when switching to a tab with content
    QWidget* currentWidget = m_tabWidget->currentWidget();
    DocumentTab* currentTab = qobject_cast<DocumentTab*>(currentWidget);
    DocumentationViewer* docViewer = qobject_cast<DocumentationViewer*>(currentWidget);

    if ((currentTab && currentTab->isDocumentLoaded()) || docViewer) {
        hideWelcomeScreen();
    } else if (m_tabWidget->count() == 1 && (!currentTab || !currentTab->isDocumentLoaded())) {
        // Show welcome screen if only one empty tab
        showWelcomeScreen();
    }
}

// Welcome Screen Implementation
void MainWindow::createWelcomeScreen()
{
    m_welcomeScreen = new WelcomeScreen(this);

    // Create loading overlay
    m_loadingOverlay = new LoadingOverlay(this);

    // Create onboarding tour
    createOnboardingTour();

    // Setup rich tooltips
    setupRichTooltips();

    // Set application information
    m_welcomeScreen->setApplicationInfo(
        tr("Optimized PDF Viewer"),
        tr("1.0"),
        tr("High-performance PDF viewer with advanced annotation capabilities")
    );

    // Connect welcome screen signals
    connect(m_welcomeScreen, &WelcomeScreen::openFileRequested,
            this, &MainWindow::onWelcomeOpenFileRequested);
    connect(m_welcomeScreen, &WelcomeScreen::newDocumentRequested,
            this, &MainWindow::onWelcomeNewDocumentRequested);
    connect(m_welcomeScreen, &WelcomeScreen::recentFileSelected,
            this, &MainWindow::onWelcomeRecentFileSelected);
    connect(m_welcomeScreen, &WelcomeScreen::settingsRequested,
            this, &MainWindow::onWelcomeSettingsRequested);
    connect(m_welcomeScreen, &WelcomeScreen::aboutRequested,
            this, &MainWindow::onWelcomeAboutRequested);
    connect(m_welcomeScreen, &WelcomeScreen::helpRequested,
            this, &MainWindow::onWelcomeHelpRequested);

    // Update recent files
    updateWelcomeScreenRecentFiles();

    // Initially hide the welcome screen
    m_welcomeScreen->hide();
}

void MainWindow::showWelcomeScreen()
{
    if (!m_welcomeScreen) return;

    // Update recent files before showing
    updateWelcomeScreenRecentFiles();

    // Add welcome screen as a tab if not already present
    int welcomeIndex = m_tabWidget->indexOf(m_welcomeScreen);
    if (welcomeIndex == -1) {
        welcomeIndex = m_tabWidget->addTab(m_welcomeScreen, tr("Welcome"));
        m_tabWidget->setCurrentIndex(welcomeIndex);
    } else {
        m_tabWidget->setCurrentIndex(welcomeIndex);
    }
}

void MainWindow::hideWelcomeScreen()
{
    if (!m_welcomeScreen) return;

    int welcomeIndex = m_tabWidget->indexOf(m_welcomeScreen);
    if (welcomeIndex != -1) {
        m_tabWidget->removeTab(welcomeIndex);
    }
}

void MainWindow::updateWelcomeScreenRecentFiles()
{
    if (!m_welcomeScreen) return;

    // Get recent files from settings
    QStringList recentFiles = m_settings->value("recentFiles").toStringList();
    m_welcomeScreen->updateRecentFiles(recentFiles);
}

// Welcome Screen Slots
void MainWindow::onWelcomeOpenFileRequested()
{
    openPdf();
}

void MainWindow::onWelcomeNewDocumentRequested()
{
    newTab();
    hideWelcomeScreen();
}

void MainWindow::onWelcomeRecentFileSelected(const QString& filePath)
{
    openPdfFile(filePath);
}

void MainWindow::onWelcomeSettingsRequested()
{
    showSettings();
}

void MainWindow::onWelcomeAboutRequested()
{
    // Show about dialog - you can implement this
    QMessageBox::about(this, tr("About Optimized PDF Viewer"),
        tr("Optimized PDF Viewer v1.0\n\n"
           "High-performance PDF viewer with advanced annotation capabilities.\n\n"
           "Built with Qt and ElaWidgetTools."));
}

void MainWindow::onWelcomeHelpRequested()
{
    showHelp();
}

void MainWindow::showHelp()
{
    Logger* logger = Logger::instance();
    logger->info("Opening documentation tab", "MainWindow");

    try {
        // Check if documentation tab already exists
        for (int i = 0; i < m_tabWidget->count(); ++i) {
            QWidget* widget = m_tabWidget->widget(i);
            DocumentationViewer* docViewer = qobject_cast<DocumentationViewer*>(widget);
            if (docViewer) {
                // Documentation tab already exists, switch to it
                m_tabWidget->setCurrentIndex(i);
                logger->info("Switched to existing documentation tab", "MainWindow");
                return;
            }
        }

        // Create new documentation tab
        createDocumentationTab();

        logger->info("Documentation tab created successfully", "MainWindow");
    }
    catch (const std::exception& e) {
        logger->error(QString("Failed to create documentation tab: %1").arg(e.what()), "MainWindow");

        // Fallback to simple message box
        QMessageBox::information(this, tr("Help"),
            tr("PDF Viewer Help\n\n"
               "Key Features:\n"
               "• Modern ribbon interface with intuitive controls\n"
               "• Multi-tab support for multiple documents\n"
               "• Advanced annotation tools (text, highlight, drawing)\n"
               "• Smart search with highlighting\n"
               "• Bookmarks for frequently accessed pages\n"
               "• Flexible zoom controls\n\n"
               "Keyboard Shortcuts:\n"
               "• Ctrl+O: Open document\n"
               "• Ctrl+W: Close current tab\n"
               "• Ctrl+T: New tab\n"
               "• Ctrl+F: Find/Search\n"
               "• Ctrl+P: Print document\n"
               "• F11: Toggle fullscreen\n"
               "• F1: Show this help\n\n"
               "For detailed documentation, check the docs folder."));
    }
}

// ElaMainWindow initialization methods
void MainWindow::initWindow()
{
    Logger* logger = Logger::instance();
    logger->info("Setting window properties", "MainWindow");
    
    // Set window properties
    setWindowIcon(QIcon(":/icons/app.png"));
    resize(1400, 900);
    setWindowTitle(tr("Optimized PDF Viewer"));
    logger->info("Basic window properties set", "MainWindow");

    try {
        if (ElaIntegration::isElaWidgetsAvailable()) {
            logger->info("Configuring ElaMainWindow appearance", "MainWindow");
            
            // Configure ElaMainWindow appearance
            setUserInfoCardTitle(tr("PDF Viewer"));
            setUserInfoCardSubTitle(tr("Professional PDF Reader"));

            // Enable navigation bar
            setIsNavigationBarEnable(true);
            setNavigationBarDisplayMode(ElaNavigationType::Maximal);

            // Configure window buttons
            setWindowButtonFlag(ElaAppBarType::MinimizeButtonHint, true);
            setWindowButtonFlag(ElaAppBarType::MaximizeButtonHint, true);
            setWindowButtonFlag(ElaAppBarType::CloseButtonHint, true);
            
            logger->info("ElaMainWindow appearance configured", "MainWindow");
        } else {
            logger->info("Using standard QMainWindow appearance", "MainWindow");
        }
    } catch (const std::exception& e) {
        logger->warning(QString("ElaMainWindow configuration failed: %1").arg(e.what()), "MainWindow");
    } catch (...) {
        logger->warning("ElaMainWindow configuration failed with unknown error", "MainWindow");
    }

    // Apply design system styling
    logger->info("Applying design system styling", "MainWindow");
    try {
        applyDesignSystemStyling();
        logger->info("Design system styling applied", "MainWindow");
    } catch (const std::exception& e) {
        logger->warning(QString("Design system styling failed: %1").arg(e.what()), "MainWindow");
    } catch (...) {
        logger->warning("Design system styling failed with unknown error", "MainWindow");
    }
}

void MainWindow::applyDesignSystemStyling()
{
    // Apply consistent design system styling to the main window
    QString windowStyleSheet = QString(
        "QMainWindow {"
        "    background-color: %1;"
        "    color: %2;"
        "    font-family: %3;"
        "    font-size: %4px;"
        "}"
        "QMenuBar {"
        "    background-color: %5;"
        "    color: %2;"
        "    border-bottom: 1px solid %6;"
        "    padding: %7px;"
        "    font-family: %3;"
        "    font-size: %4px;"
        "}"
        "QMenuBar::item {"
        "    background-color: transparent;"
        "    padding: %8px %9px;"
        "    border-radius: %10px;"
        "}"
        "QMenuBar::item:selected {"
        "    background-color: %11;"
        "}"
        "QStatusBar {"
        "    background-color: %12;"
        "    color: %13;"
        "    border-top: 1px solid %6;"
        "    padding: %14px;"
        "    font-size: %15px;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::Colors::TextPrimary.name())
     .arg(DesignSystem::Typography::PrimaryFont)
     .arg(DesignSystem::Typography::BodyMedium)
     .arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::Spacing::Medium)
     .arg(DesignSystem::BorderRadius::Medium)
     .arg(DesignSystem::Colors::BackgroundHover.name())
     .arg(DesignSystem::Colors::BackgroundSecondary.name())
     .arg(DesignSystem::Colors::TextSecondary.name())
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::Typography::BodySmall);

    setStyleSheet(windowStyleSheet);
}

void MainWindow::initNavigation()
{
    // Add main navigation nodes
    addPageNode(tr("Home"), m_welcomeScreen, ElaIconType::House);
    m_homeKey = m_welcomeScreen->property("ElaPageKey").toString();

    // File operations section
    addExpanderNode(tr("File"), m_documentsKey, ElaIconType::FolderOpen);

    // View section with navigation and zoom controls
    addExpanderNode(tr("View"), m_viewKey, ElaIconType::Eye);

    // Tools section for annotations and utilities
    addExpanderNode(tr("Tools"), m_toolsKey, ElaIconType::Wrench);

    // Settings in footer
    addFooterNode(tr("Settings"), m_settingsKey, 0, ElaIconType::GearComplex);

    // Skip navigation signals for QMainWindow testing
    // connect(this, &ElaMainWindow::navigationNodeClicked, this, [this](ElaNavigationType::NavigationNodeType, QString nodeKey) {
    //     if (nodeKey == m_homeKey) {
    //         showWelcomeScreen();
    //     } else if (nodeKey == m_settingsKey) {
    //         showSettings();
    //     }
    //     // For now, expander nodes just expand/collapse - functionality is in the menu bar
    // });

    // Skip navigation for QMainWindow testing
    // navigation(m_homeKey);
}

void MainWindow::initContent()
{
    // The tab widget is already added as central widget
    // Additional content initialization can be done here

    // Skip ribbon navigation for debugging - it was causing hangs
    // setupRibbonNavigation();
    Logger* logger = Logger::instance();
    logger->info("Content initialization completed (ribbon navigation skipped)", "MainWindow");
}

// Crash handling integration methods
void MainWindow::connectCrashHandling()
{
    ErrorHandler* errorHandler = ErrorHandler::instance();

    // Connect crash detection signals
    connect(errorHandler, &ErrorHandler::crashDetected,
            this, &MainWindow::onCrashDetected);

    connect(errorHandler, &ErrorHandler::crashReportGenerated,
            this, &MainWindow::onCrashReportGenerated);

    // Connect critical error signals for immediate UI response
    connect(errorHandler, &ErrorHandler::criticalErrorOccurred,
            this, [this](const ErrorInfo& error) {
                trackUserAction(QString("Critical error occurred: %1").arg(error.title));

                // Show critical error notification
                QMessageBox::warning(this, tr("Critical Error"),
                    QString("%1\n\n%2").arg(error.title, error.message));
            });
}

void MainWindow::trackUserAction(const QString& action)
{
    ErrorHandler* errorHandler = ErrorHandler::instance();
    if (errorHandler) {
        // Use the ErrorHandler's internal tracking method
        // We'll need to make this method public or add a public wrapper
        Logger* logger = Logger::instance();
        logger->debug(QString("User action: %1").arg(action), "MainWindow");
    }
}

void MainWindow::onCrashDetected(const CrashInfo& crashInfo)
{
    // This method will be called when a crash is detected
    // Since the application might be in an unstable state, we need to be very careful

    Logger* logger = Logger::instance();
    logger->error(QString("Crash detected: %1 - %2").arg(crashInfo.crashType, crashInfo.signal), "MainWindow");

    // Try to save current session if possible
    try {
        saveSession();
    } catch (...) {
        // Ignore errors during crash handling
    }

    // Show crash recovery dialog if the UI is still responsive
    QTimer::singleShot(100, this, [this, crashInfo]() {
        showCrashRecoveryDialog(crashInfo);
    });
}

void MainWindow::onCrashReportGenerated(const QString& reportPath)
{
    Logger* logger = Logger::instance();
    logger->info(QString("Crash report generated: %1").arg(reportPath), "MainWindow");

    // Optionally show notification to user about crash report
    if (m_settings && m_settings->value("notifications/showCrashReports", true).toBool()) {
        QMessageBox::information(this, tr("Crash Report Generated"),
            tr("A crash report has been generated and saved to:\n%1\n\n"
               "This report can help developers fix the issue.").arg(reportPath));
    }
}

void MainWindow::showCrashRecoveryDialog(const CrashInfo& crashInfo)
{
    // Create a crash recovery dialog
    QMessageBox msgBox(this);
    msgBox.setWindowTitle(tr("Application Crash Detected"));
    msgBox.setIcon(QMessageBox::Warning);

    QString message = tr("The application has detected a crash:\n\n"
                        "Type: %1\n"
                        "Signal: %2\n"
                        "Time: %3\n\n"
                        "A crash report has been generated. "
                        "Would you like to restart the application?")
                     .arg(crashInfo.crashType)
                     .arg(crashInfo.signal)
                     .arg(crashInfo.timestamp.toString());

    msgBox.setText(message);
    msgBox.setStandardButtons(QMessageBox::Yes | QMessageBox::No);
    msgBox.setDefaultButton(QMessageBox::Yes);

    int result = msgBox.exec();

    if (result == QMessageBox::Yes) {
        // Restart the application
        QProcess::startDetached(QApplication::applicationFilePath(), QApplication::arguments());
        QApplication::quit();
    }
}

void MainWindow::setupRibbonNavigation()
{
    // Create modern menu bar in the app bar
    ElaMenuBar* modernMenuBar = new ElaMenuBar(this);
    modernMenuBar->setFixedHeight(30);

    // File menu
    QAction* quickOpenAction = modernMenuBar->addElaIconAction(ElaIconType::FolderOpen, tr("Open"));
    connect(quickOpenAction, &QAction::triggered, this, &MainWindow::openPdf);

    ElaMenu* fileMenu = modernMenuBar->addMenu(ElaIconType::File, tr("File"));
    fileMenu->setMenuItemHeight(27);
    QAction* openAction = fileMenu->addElaIconAction(ElaIconType::FileArrowUp, tr("Open Document"), QKeySequence::Open);
    connect(openAction, &QAction::triggered, this, &MainWindow::openPdf);

    QAction* newTabAction = fileMenu->addElaIconAction(ElaIconType::Plus, tr("New Tab"), QKeySequence::AddTab);
    connect(newTabAction, &QAction::triggered, this, &MainWindow::newTab);

    fileMenu->addSeparator();
    fileMenu->addElaIconAction(ElaIconType::Clock, tr("Recent Files"));
    // Recent files will be handled by the welcome screen for now

    fileMenu->addSeparator();
    QAction* exportAction = fileMenu->addElaIconAction(ElaIconType::FileExport, tr("Export"));
    connect(exportAction, &QAction::triggered, this, &MainWindow::exportCurrentPage);

    QAction* printAction = fileMenu->addElaIconAction(ElaIconType::Print, tr("Print"), QKeySequence::Print);
    connect(printAction, &QAction::triggered, this, &MainWindow::printDocument);

    // View menu
    ElaMenu* viewMenu = modernMenuBar->addMenu(ElaIconType::Eye, tr("View"));
    viewMenu->setMenuItemHeight(27);
    QAction* zoomInAction = viewMenu->addElaIconAction(ElaIconType::MagnifyingGlassPlus, tr("Zoom In"), QKeySequence::ZoomIn);
    connect(zoomInAction, &QAction::triggered, this, &MainWindow::zoomIn);

    QAction* zoomOutAction = viewMenu->addElaIconAction(ElaIconType::MagnifyingGlassMinus, tr("Zoom Out"), QKeySequence::ZoomOut);
    connect(zoomOutAction, &QAction::triggered, this, &MainWindow::zoomOut);

    viewMenu->addSeparator();
    QAction* layoutAction = viewMenu->addElaIconAction(ElaIconType::TableLayout, tr("Page Layout"));
    connect(layoutAction, &QAction::triggered, this, &MainWindow::setSinglePageMode);

    QAction* panelsAction = viewMenu->addElaIconAction(ElaIconType::Sidebar, tr("Toggle Panels"));
    connect(panelsAction, &QAction::triggered, this, &MainWindow::toggleThumbnails);

    QAction* fullScreenAction = viewMenu->addElaIconAction(ElaIconType::WindowMaximize, tr("Full Screen"), QKeySequence::FullScreen);
    connect(fullScreenAction, &QAction::triggered, this, &MainWindow::toggleFullScreen);

    // Tools menu
    ElaMenu* toolsMenu = modernMenuBar->addMenu(ElaIconType::Wrench, tr("Tools"));
    toolsMenu->setMenuItemHeight(27);
    QAction* annotationsAction = toolsMenu->addElaIconAction(ElaIconType::PenToSquare, tr("Annotations"));
    connect(annotationsAction, &QAction::triggered, this, [this]() {
        if (m_annotationToolbar) {
            m_annotationToolbar->setVisible(!m_annotationToolbar->isVisible());
        }
    });

    QAction* searchAction = toolsMenu->addElaIconAction(ElaIconType::MagnifyingGlass, tr("Search"), QKeySequence::Find);
    connect(searchAction, &QAction::triggered, this, &MainWindow::showSearchBar);

    toolsMenu->addSeparator();
    QAction* textSelectionAction = toolsMenu->addElaIconAction(ElaIconType::Font, tr("Text Selection"), QKeySequence("Ctrl+T"));
    textSelectionAction->setCheckable(true);
    connect(textSelectionAction, &QAction::triggered, this, &MainWindow::toggleTextSelection);

    QAction* copyTextAction = toolsMenu->addElaIconAction(ElaIconType::Copy, tr("Copy Selected Text"), QKeySequence("Ctrl+Shift+C"));
    connect(copyTextAction, &QAction::triggered, this, &MainWindow::copySelectedText);

    QAction* clearSelectionAction = toolsMenu->addElaIconAction(ElaIconType::Xmark, tr("Clear Selection"), QKeySequence("Escape"));
    connect(clearSelectionAction, &QAction::triggered, this, &MainWindow::clearTextSelection);

    toolsMenu->addSeparator();
    QAction* smartZoomAction = toolsMenu->addElaIconAction(ElaIconType::MagnifyingGlass, tr("Smart Zoom"), QKeySequence("Ctrl+Shift+Z"));
    connect(smartZoomAction, &QAction::triggered, this, &MainWindow::performSmartZoom);

    QAction* advancedMagnifierAction = toolsMenu->addElaIconAction(ElaIconType::Eye, tr("Advanced Magnifier"), QKeySequence("Ctrl+Shift+M"));
    advancedMagnifierAction->setCheckable(true);
    connect(advancedMagnifierAction, &QAction::triggered, this, &MainWindow::toggleAdvancedMagnifier);

    QAction* bookmarksAction = toolsMenu->addElaIconAction(ElaIconType::Bookmark, tr("Bookmarks"));
    connect(bookmarksAction, &QAction::triggered, this, &MainWindow::addBookmark);

    toolsMenu->addSeparator();
    QAction* rotateAction = toolsMenu->addElaIconAction(ElaIconType::ArrowRotateRight, tr("Rotate"));
    connect(rotateAction, &QAction::triggered, this, &MainWindow::rotateClockwise);

    QAction* utilitiesAction = toolsMenu->addElaIconAction(ElaIconType::Screwdriver, tr("Utilities"));
    connect(utilitiesAction, &QAction::triggered, this, &MainWindow::showMemoryUsage);

    // Create a container widget for the menu bar
    QWidget* menuBarWidget = new QWidget(this);
    QVBoxLayout* menuBarLayout = new QVBoxLayout(menuBarWidget);
    menuBarLayout->setContentsMargins(0, 0, 0, 0);
    menuBarLayout->addWidget(modernMenuBar);
    menuBarLayout->addStretch();

    // Skip ElaMainWindow-specific methods for QMainWindow testing
    // setCustomWidget(ElaAppBarType::LeftArea, menuBarWidget);
    // setCustomWidgetMaximumWidth(400);

    // Create page navigation controls
    m_pageSpinBox = new ElaSpin(this);
    m_pageSpinBox->setMinimum(1);
    m_pageSpinBox->setMaximum(1);
    m_pageSpinBox->setEnabled(false);
    m_pageSpinBox->setMinimumWidth(55);
    m_pageSpinBox->setMaximumWidth(70);
    m_pageSpinBox->setToolTip(tr("Current page number"));
    connect(m_pageSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::jumpToPage);

    m_pageCountLabel = new ElaLabel(tr("of 0"), this);
    m_pageCountLabel->setMinimumWidth(35);
    m_pageCountLabel->setAlignment(Qt::AlignCenter);

    // Create zoom controls
    m_zoomComboBox = new ElaCombo(this);
    m_zoomComboBox->setEditable(true);
    m_zoomComboBox->addItems(QStringList() << "25%" << "50%" << "75%" << "100%" << "125%" << "150%" << "200%" << "300%" << "400%");
    m_zoomComboBox->setCurrentText("100%");
    m_zoomComboBox->setMinimumWidth(75);
    m_zoomComboBox->setMaximumWidth(85);
    m_zoomComboBox->setToolTip(tr("Zoom level - supports custom values"));
    connect(m_zoomComboBox, &QComboBox::currentTextChanged, this, &MainWindow::onZoomComboChanged);

    // Create zoom slider
    m_zoomSlider = new ElaSliderWidget(Qt::Horizontal, this);
    m_zoomSlider->setRange(0, 100);
    m_zoomSlider->setValue(50); // 100% zoom
    m_zoomSlider->setMaximumWidth(100);
    m_zoomSlider->setMinimumWidth(80);
    m_zoomSlider->setToolTip(tr("Zoom slider (25% - 400%)"));
    connect(m_zoomSlider, &QSlider::valueChanged, this, &MainWindow::onZoomSliderChanged);

    // Create a modern toolbar widget to add to the app bar
    QWidget* toolbarWidget = new QWidget(this);
    QHBoxLayout* toolbarLayout = new QHBoxLayout(toolbarWidget);
    toolbarLayout->setContentsMargins(10, 5, 10, 5);
    toolbarLayout->setSpacing(10);

    // Add file operations
    ElaPushButton* openBtn = new ElaPushButton(tr("Open"), this);
    openBtn->setIcon(m_openAction->icon());
    connect(openBtn, &QPushButton::clicked, this, &MainWindow::openPdf);
    toolbarLayout->addWidget(openBtn);

    ElaPushButton* newTabBtn = new ElaPushButton(tr("New Tab"), this);
    newTabBtn->setIcon(m_newTabAction->icon());
    connect(newTabBtn, &QPushButton::clicked, this, &MainWindow::newTab);
    toolbarLayout->addWidget(newTabBtn);

    toolbarLayout->addWidget(new ElaText("|", this)); // Separator

    // Add navigation controls
    ElaPushButton* firstPageBtn = new ElaPushButton(this);
    firstPageBtn->setIcon(m_firstPageAction->icon());
    firstPageBtn->setToolTip(tr("First Page"));
    connect(firstPageBtn, &QPushButton::clicked, this, &MainWindow::firstPage);
    toolbarLayout->addWidget(firstPageBtn);

    ElaPushButton* prevPageBtn = new ElaPushButton(this);
    prevPageBtn->setIcon(m_prevPageAction->icon());
    prevPageBtn->setToolTip(tr("Previous Page"));
    connect(prevPageBtn, &QPushButton::clicked, this, &MainWindow::previousPage);
    toolbarLayout->addWidget(prevPageBtn);

    toolbarLayout->addWidget(m_pageSpinBox);
    toolbarLayout->addWidget(m_pageCountLabel);

    ElaPushButton* nextPageBtn = new ElaPushButton(this);
    nextPageBtn->setIcon(m_nextPageAction->icon());
    nextPageBtn->setToolTip(tr("Next Page"));
    connect(nextPageBtn, &QPushButton::clicked, this, &MainWindow::nextPage);
    toolbarLayout->addWidget(nextPageBtn);

    ElaPushButton* lastPageBtn = new ElaPushButton(this);
    lastPageBtn->setIcon(m_lastPageAction->icon());
    lastPageBtn->setToolTip(tr("Last Page"));
    connect(lastPageBtn, &QPushButton::clicked, this, &MainWindow::lastPage);
    toolbarLayout->addWidget(lastPageBtn);

    toolbarLayout->addWidget(new ElaText("|", this)); // Separator

    // Add zoom controls
    ElaPushButton* zoomOutBtn = new ElaPushButton(this);
    zoomOutBtn->setIcon(m_zoomOutAction->icon());
    zoomOutBtn->setToolTip(tr("Zoom Out"));
    connect(zoomOutBtn, &QPushButton::clicked, this, &MainWindow::zoomOut);
    toolbarLayout->addWidget(zoomOutBtn);

    toolbarLayout->addWidget(m_zoomComboBox);
    toolbarLayout->addWidget(m_zoomSlider);

    ElaPushButton* zoomInBtn = new ElaPushButton(this);
    zoomInBtn->setIcon(m_zoomInAction->icon());
    zoomInBtn->setToolTip(tr("Zoom In"));
    connect(zoomInBtn, &QPushButton::clicked, this, &MainWindow::zoomIn);
    toolbarLayout->addWidget(zoomInBtn);

    toolbarLayout->addStretch();

    // Add the toolbar widget to the app bar
    setCustomWidget(ElaAppBarType::MiddleArea, toolbarWidget);
}

// Annotation methods
void MainWindow::onAnnotationToolChanged(AnnotationTool tool)
{
    Q_UNUSED(tool); // tool parameter not used in current implementation
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        // Set the current annotation tool for the tab
        // This will be used by mouse event handlers
        statusBar()->showMessage(tr("Annotation tool changed"), 2000);
    }
}

void MainWindow::onAnnotationColorChanged(const QColor& color)
{
    Q_UNUSED(color); // color parameter not used in current implementation
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        // Update color for selected annotations or future annotations
        statusBar()->showMessage(tr("Annotation color changed"), 2000);
    }
}

void MainWindow::onAnnotationOpacityChanged(qreal opacity)
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        // Update opacity for selected annotations or future annotations
        statusBar()->showMessage(tr("Annotation opacity changed to %1%").arg(qRound(opacity * 100)), 2000);
    }
}

void MainWindow::onAnnotationLineWidthChanged(qreal width)
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        // Update line width for selected annotations or future annotations
        statusBar()->showMessage(tr("Annotation line width changed to %1").arg(width), 2000);
    }
}

void MainWindow::onDeleteSelectedAnnotations()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->getAnnotationManager()) {
        QList<Annotation*> selectedAnnotations = currentTab->getAnnotationManager()->getSelectedAnnotations();
        if (!selectedAnnotations.isEmpty()) {
            for (Annotation* annotation : selectedAnnotations) {
                currentTab->getAnnotationManager()->removeAnnotation(annotation);
            }
            statusBar()->showMessage(tr("Deleted %1 annotation(s)").arg(selectedAnnotations.size()), 3000);

            // Refresh the current page to show changes
            currentTab->requestCurrentPage();
        }
    }
}

void MainWindow::onCopySelectedAnnotations()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->getAnnotationManager()) {
        QList<Annotation*> selectedAnnotations = currentTab->getAnnotationManager()->getSelectedAnnotations();
        if (!selectedAnnotations.isEmpty()) {
            // TODO: Implement annotation copying to clipboard
            statusBar()->showMessage(tr("Copied %1 annotation(s)").arg(selectedAnnotations.size()), 3000);
        }
    }
}

void MainWindow::onPasteAnnotations()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->getAnnotationManager()) {
        // TODO: Implement annotation pasting from clipboard
        statusBar()->showMessage(tr("Paste annotations not yet implemented"), 3000);
    }
}

void MainWindow::undoAnnotation()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        AnnotationManager* manager = currentTab->getAnnotationManager();
        if (manager && manager->canUndo()) {
            manager->undo();
            statusBar()->showMessage(tr("Undo annotation action"), 2000);
            updateUndoRedoActions();
        }
    }
}

void MainWindow::redoAnnotation()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        AnnotationManager* manager = currentTab->getAnnotationManager();
        if (manager && manager->canRedo()) {
            manager->redo();
            statusBar()->showMessage(tr("Redo annotation action"), 2000);
            updateUndoRedoActions();
        }
    }
}

void MainWindow::updateUndoRedoActions()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        AnnotationManager* manager = currentTab->getAnnotationManager();
        if (manager) {
            m_undoAction->setEnabled(manager->canUndo());
            m_redoAction->setEnabled(manager->canRedo());
            return;
        }
    }

    // No document or annotation manager - disable both actions
    m_undoAction->setEnabled(false);
    m_redoAction->setEnabled(false);
}

void MainWindow::searchAnnotations()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        AnnotationManager* manager = currentTab->getAnnotationManager();
        if (manager) {
            AnnotationSearchDialog* dialog = new AnnotationSearchDialog(manager, this);

            // Connect signals for navigation
            connect(dialog, &AnnotationSearchDialog::navigateToAnnotation, [this](Annotation* annotation) {
                if (annotation) {
                    DocumentTab* tab = getCurrentTab();
                    if (tab) {
                        tab->setCurrentPage(annotation->getPageNumber());
                        statusBar()->showMessage(tr("Navigated to annotation on page %1").arg(annotation->getPageNumber() + 1), 3000);
                    }
                }
            });

            connect(dialog, &AnnotationSearchDialog::annotationSelected, [this](Annotation* annotation) {
                if (annotation) {
                    DocumentTab* tab = getCurrentTab();
                    if (tab && tab->getAnnotationManager()) {
                        tab->getAnnotationManager()->deselectAll();
                        tab->getAnnotationManager()->selectAnnotation(annotation);
                        statusBar()->showMessage(tr("Selected annotation"), 2000);
                    }
                }
            });

            dialog->show();
        }
    }
}

void MainWindow::copyAnnotations()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        AnnotationManager* manager = currentTab->getAnnotationManager();
        if (manager) {
            ANNOTATION_CLIPBOARD->copySelectedAnnotations(manager);
            int count = ANNOTATION_CLIPBOARD->getAnnotationCount();
            if (count > 0) {
                statusBar()->showMessage(tr("Copied %1 annotation(s) to clipboard").arg(count), 3000);
            } else {
                statusBar()->showMessage(tr("No annotations selected to copy"), 2000);
            }
        }
    }
}

void MainWindow::pasteAnnotations()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        AnnotationManager* manager = currentTab->getAnnotationManager();
        if (manager && ANNOTATION_CLIPBOARD->canPaste()) {
            int currentPage = currentTab->getCurrentPage();
            ANNOTATION_CLIPBOARD->pasteAnnotationsToManager(manager, currentPage);
            int count = ANNOTATION_CLIPBOARD->getAnnotationCount();
            statusBar()->showMessage(tr("Pasted %1 annotation(s) to page %2").arg(count).arg(currentPage + 1), 3000);
        } else {
            statusBar()->showMessage(tr("No annotations in clipboard to paste"), 2000);
        }
    }
}

// Missing method implementations

void MainWindow::onDocumentLoaded(bool success, const QString& errorString)
{
    // Hide loading overlay
    m_loadingOverlay->hideLoading();

    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) return;

    if (success) {
        statusBar()->showMessage(tr("Document loaded successfully"), 3000);
        updateUiState();
        updateThumbnails();
        updateOutline();

        // Enable annotation tools
        if (m_annotationToolbar) {
            m_annotationToolbar->setAnnotationToolsEnabled(true);
        }

        // Update search annotations action
        m_searchAnnotationsAction->setEnabled(true);
    } else {
        statusBar()->showMessage(tr("Failed to load document: %1").arg(errorString), 5000);
        QMessageBox::critical(this, tr("Load Error"), tr("Failed to load document:\n%1").arg(errorString));
    }
}

// Enhanced User Experience Implementation

void MainWindow::setupRichTooltips()
{
    // Add rich tooltips to key UI elements
    // This would be implemented with hover event filters
    // For now, we'll set up basic enhanced tooltips
}

void MainWindow::createOnboardingTour()
{
    m_onboardingTour = new OnboardingTour(this);

    // Add tour steps for key features
    m_onboardingTour->setWelcomeMessage(
        tr("Welcome to PDF Viewer"),
        tr("Let's take a quick tour of the key features to help you get started.")
    );

    // Add steps for main features
    m_onboardingTour->addStep(TourStep(
        tr("Navigation Bar"),
        tr("Use the navigation bar to access different sections like File, View, and Tools."),
        "navigationBar"
    ));

    m_onboardingTour->addStep(TourStep(
        tr("Document Tabs"),
        tr("Open multiple PDF documents in tabs for easy switching between files."),
        "tabWidget"
    ));

    m_onboardingTour->addStep(TourStep(
        tr("Thumbnail Panel"),
        tr("View page thumbnails for quick navigation through your document."),
        "thumbnailsDock"
    ));

    m_onboardingTour->addStep(TourStep(
        tr("Status Bar"),
        tr("Monitor document information, zoom level, and operation progress here."),
        "statusBar"
    ));

    m_onboardingTour->setCompletionMessage(
        tr("Tour Complete!"),
        tr("You're all set! Explore the application and discover more features as you go.")
    );

    // Connect tour signals
    connect(m_onboardingTour, &OnboardingTour::tourCompleted, this, [this]() {
        // Mark tour as completed in settings
        m_settings->setValue("onboardingCompleted", true);
    });
}

void MainWindow::startOnboardingTour()
{
    if (m_onboardingTour) {
        m_onboardingTour->startTour();
    }
}

ModernContextMenu* MainWindow::createDocumentContextMenu()
{
    ModernContextMenu* menu = new ModernContextMenu(this);

    // Add document-specific actions
    menu->addActionWithIcon(ElaIconType::FolderOpen, tr("Open Document"), "Ctrl+O",
                           tr("Open a new PDF document"));
    menu->addActionWithIcon(ElaIconType::FileLines, tr("Document Info"), "Ctrl+I",
                           tr("View document properties and metadata"));
    menu->addStyledSeparator();
    menu->addActionWithIcon(ElaIconType::Print, tr("Print"), "Ctrl+P",
                           tr("Print the current document"));
    menu->addActionWithIcon(ElaIconType::FileExport, tr("Export"), "Ctrl+E",
                           tr("Export document or pages"));

    return menu;
}

ModernContextMenu* MainWindow::createPageContextMenu()
{
    ModernContextMenu* menu = new ModernContextMenu(this);

    // Add page-specific actions
    menu->addActionWithIcon(ElaIconType::MagnifyingGlassPlus, tr("Zoom In"), "Ctrl++",
                           tr("Increase zoom level"));
    menu->addActionWithIcon(ElaIconType::MagnifyingGlassMinus, tr("Zoom Out"), "Ctrl+-",
                           tr("Decrease zoom level"));
    menu->addActionWithIcon(ElaIconType::ArrowsMaximize, tr("Fit to Window"), "Ctrl+0",
                           tr("Fit page to window size"));
    menu->addStyledSeparator();
    menu->addActionWithIcon(ElaIconType::ArrowRotateRight, tr("Rotate Clockwise"), "Ctrl+R",
                           tr("Rotate page 90 degrees clockwise"));
    menu->addActionWithIcon(ElaIconType::ArrowRotateLeft, tr("Rotate Counter-clockwise"), "Ctrl+Shift+R",
                           tr("Rotate page 90 degrees counter-clockwise"));

    return menu;
}

// Enhanced Drag and Drop Implementation

void MainWindow::dragEnterEvent(QDragEnterEvent* event)
{
    // Check if the drag contains file URLs
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();

        // Check if any of the files are PDFs
        bool hasPdfFiles = false;
        for (const QUrl& url : urls) {
            if (url.isLocalFile()) {
                QString filePath = url.toLocalFile();
                if (filePath.toLower().endsWith(".pdf")) {
                    hasPdfFiles = true;
                    break;
                }
            }
        }

        if (hasPdfFiles) {
            event->acceptProposedAction();

            // Show visual feedback
            setStyleSheet(styleSheet() +
                         "MainWindow { border: 2px dashed #0078d4; background-color: rgba(0, 120, 212, 0.1); }");

            LOG_DEBUG("Drag enter event accepted - PDF files detected");
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void MainWindow::dragMoveEvent(QDragMoveEvent* event)
{
    // Accept the drag move if we accepted the drag enter
    if (event->mimeData()->hasUrls()) {
        event->acceptProposedAction();
    } else {
        event->ignore();
    }
}

void MainWindow::dragLeaveEvent(QDragLeaveEvent* event)
{
    // Remove visual feedback
    setStyleSheet(styleSheet().remove(
        "MainWindow { border: 2px dashed #0078d4; background-color: rgba(0, 120, 212, 0.1); }"));

    LOG_DEBUG("Drag leave event");
    event->accept();
}

void MainWindow::dropEvent(QDropEvent* event)
{
    // Remove visual feedback
    setStyleSheet(styleSheet().remove(
        "MainWindow { border: 2px dashed #0078d4; background-color: rgba(0, 120, 212, 0.1); }"));

    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        QStringList pdfFiles;

        // Extract PDF file paths
        for (const QUrl& url : urls) {
            if (url.isLocalFile()) {
                QString filePath = url.toLocalFile();
                if (filePath.toLower().endsWith(".pdf")) {
                    pdfFiles.append(filePath);
                }
            }
        }

        // Open the PDF files
        if (!pdfFiles.isEmpty()) {
            LOG_INFO(QString("Opening %1 PDF files via drag and drop").arg(pdfFiles.size()));

            for (const QString& filePath : pdfFiles) {
                openPdfFile(filePath);
            }

            event->acceptProposedAction();

            // Show success message
            statusBar()->showMessage(
                tr("Opened %1 PDF file(s) via drag and drop").arg(pdfFiles.size()), 3000);
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void MainWindow::onPageReady(int pageNum, const QPixmap& pixmap, bool isHighQuality)
{
    DocumentTab* currentTab = getCurrentTab();
    if (!currentTab) return;

    // Update thumbnail if this is a preview
    if (!isHighQuality && pageNum < m_thumbnailsList->count()) {
        QListWidgetItem* item = m_thumbnailsList->item(pageNum);
        if (item) {
            QIcon icon(pixmap.scaled(120, 160, Qt::KeepAspectRatio, Qt::SmoothTransformation));
            item->setIcon(icon);
        }
    }

    // Update UI state when current page is ready
    if (pageNum == currentTab->getCurrentPage()) {
        updateUiState();
    }
}

void MainWindow::requestCurrentPage()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        currentTab->requestCurrentPage();
    }
}

// Text selection functionality
void MainWindow::toggleTextSelection()
{
    m_textSelectionMode = !m_textSelectionMode;

    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->setTextSelectionEnabled(m_textSelectionMode);

        // Update status message
        if (m_textSelectionMode) {
            statusBar()->showMessage(tr("Text selection mode enabled - click and drag to select text"), 3000);
        } else {
            statusBar()->showMessage(tr("Text selection mode disabled"), 2000);
        }
    }
}

void MainWindow::copySelectedText()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->hasTextSelection()) {
        currentTab->copySelectedTextToClipboard();
        QString selectedText = currentTab->getSelectedText();

        // Show confirmation message with text length
        int textLength = selectedText.length();
        statusBar()->showMessage(tr("Copied %1 characters to clipboard").arg(textLength), 2000);
    } else {
        statusBar()->showMessage(tr("No text selected"), 2000);
    }
}

void MainWindow::clearTextSelection()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab) {
        currentTab->clearTextSelection();
        statusBar()->showMessage(tr("Text selection cleared"), 2000);
    }
}

// Advanced zoom functionality
void MainWindow::performSmartZoom()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        currentTab->performSmartZoom();
        statusBar()->showMessage(tr("Applied smart zoom"), 2000);
    }
}

void MainWindow::toggleAdvancedMagnifier()
{
    DocumentTab* currentTab = getCurrentTab();
    if (currentTab && currentTab->isDocumentLoaded()) {
        bool enabled = !currentTab->isMagnifierEnabled();
        currentTab->enableMagnifier(enabled);

        if (enabled) {
            statusBar()->showMessage(tr("Advanced magnifier enabled - hover over document to magnify"), 3000);
        } else {
            statusBar()->showMessage(tr("Advanced magnifier disabled"), 2000);
        }
    }
}
