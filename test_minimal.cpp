#include <QApplication>
#include <QDebug>
#include "ElaIntegration.h"
#include "ElaWindow.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    
    qDebug() << "Starting minimal test";
    
    try {
        // Initialize ElaWidgetTools
        qDebug() << "Initializing ElaWidgetTools";
        ElaIntegration::initializeElaApplication();
        qDebug() << "ElaWidgetTools initialized";
        
        // Create minimal ElaWindow
        qDebug() << "Creating ElaWindow";
        ElaWindow window;
        qDebug() << "ElaWindow created";
        
        // Basic setup
        qDebug() << "Setting up window";
        window.setWindowTitle("Minimal Test");
        window.resize(800, 600);
        qDebug() << "Window setup complete";
        
        // Try to show
        qDebug() << "About to show window";
        window.show();
        qDebug() << "Window shown successfully";
        
        return a.exec();
        
    } catch (const std::exception& e) {
        qDebug() << "Exception:" << e.what();
        return -1;
    } catch (...) {
        qDebug() << "Unknown exception";
        return -1;
    }
}
