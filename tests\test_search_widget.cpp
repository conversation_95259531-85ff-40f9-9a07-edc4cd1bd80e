#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QLineEdit>
#include <QCheckBox>
#include <QPushButton>
#include <QTimer>
#include <QCompleter>
#include <QStringListModel>
#include <QApplication>
#include <QWidget>
#include <QProgressBar>

#include "SearchWidget.h"
#include "SearchHistory.h"

class TestSearchWidget : public QWidget
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testSearchTermGetterSetter();
    void testCaseSensitiveOption();
    void testWholeWordsOption();
    void testHighlightAllOption();
    void testSearchFieldFocus();

    // Search execution tests
    void testPerformSearch();
    void testPerformSearchWithEmptyTerm();
    void testClearSearch();
    void testSearchSignalEmission();
    void testSearchWithOptions();

    // Search state tests
    void testSearchInProgress();
    void testSearchResults();
    void testClearSearchResults();
    void testSearchResultsDisplay();

    // Auto-search functionality
    void testAutoSearchEnabled();
    void testAutoSearchDisabled();
    void testAutoSearchDelay();
    void testAutoSearchTimeout();
    void testAutoSearchCancellation();

    // Search history integration
    void testSearchHistoryIntegration();
    void testHistoryItemSelection();
    void testHistoryMenuCreation();
    void testHistoryCompleter();
    void testHistorySignals();

    // UI component tests
    void testUIComponentsCreation();
    void testButtonStates();
    void testNavigationButtons();
    void testProgressIndicator();
    void testOptionsCheckboxes();

    // Widget state tests
    void testWidgetEnabled();
    void testWidgetVisible();
    void testSearchFieldPlaceholder();

    // Event handling tests
    void testSearchTextChanged();
    void testSearchTextEdited();
    void testSearchOptionsChanged();
    void testCompleterActivation();

    // Styling tests
    void testSearchStyling();
    void testFoundResultsStyling();
    void testNotFoundStyling();
    void testSearchingStateStyling();

    // Edge cases and validation
    void testEmptySearchHandling();
    void testLongSearchTerms();
    void testSpecialCharacters();
    void testSearchWithoutHistory();

    // Integration tests
    void testSearchWorkflow();
    void testHistoryWorkflow();
    void testAutoSearchWorkflow();

private:
    SearchWidget* m_searchWidget;
    SearchHistory* m_searchHistory;
    
    void simulateUserTyping(const QString& text, int delayMs = 50);
    void waitForAutoSearch(int timeoutMs = 1000);
    bool hasSearchOption(const QString& optionName);
};

void TestSearchWidget::initTestCase()
{
    // Initialize application if not already done
    if (!QApplication::instance()) {
        int argc = 0;
        char** argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestSearchWidget::cleanupTestCase()
{
    // Cleanup handled by Qt
}

void TestSearchWidget::init()
{
    // Create fresh search widget and history for each test
    m_searchHistory = new SearchHistory(this);
    m_searchWidget = new SearchWidget(this);
    m_searchWidget->setSearchHistory(m_searchHistory);
}

void TestSearchWidget::cleanup()
{
    delete m_searchWidget;
    delete m_searchHistory;
    m_searchWidget = nullptr;
    m_searchHistory = nullptr;
}

void TestSearchWidget::testSearchTermGetterSetter()
{
    // Test initial state
    QVERIFY(m_searchWidget->getSearchTerm().isEmpty());
    
    // Test setting search term
    QString testTerm = "test search term";
    m_searchWidget->setSearchTerm(testTerm);
    QCOMPARE(m_searchWidget->getSearchTerm(), testTerm);
    
    // Test setting empty term
    m_searchWidget->setSearchTerm("");
    QVERIFY(m_searchWidget->getSearchTerm().isEmpty());
    
    // Test setting term with whitespace
    m_searchWidget->setSearchTerm("  spaced term  ");
    QCOMPARE(m_searchWidget->getSearchTerm(), QString("spaced term")); // Should be trimmed
}

void TestSearchWidget::testCaseSensitiveOption()
{
    // Test initial state
    QVERIFY(!m_searchWidget->isCaseSensitive()); // Should default to false
    
    // Test setting case sensitive
    m_searchWidget->setCaseSensitive(true);
    QVERIFY(m_searchWidget->isCaseSensitive());
    
    // Test unsetting case sensitive
    m_searchWidget->setCaseSensitive(false);
    QVERIFY(!m_searchWidget->isCaseSensitive());
}

void TestSearchWidget::testWholeWordsOption()
{
    // Test initial state
    QVERIFY(!m_searchWidget->isWholeWords()); // Should default to false
    
    // Test setting whole words
    m_searchWidget->setWholeWords(true);
    QVERIFY(m_searchWidget->isWholeWords());
    
    // Test unsetting whole words
    m_searchWidget->setWholeWords(false);
    QVERIFY(!m_searchWidget->isWholeWords());
}

void TestSearchWidget::testHighlightAllOption()
{
    // Test initial state (should default to true based on implementation)
    QVERIFY(m_searchWidget->isHighlightAll());
    
    // Test unsetting highlight all
    m_searchWidget->setHighlightAll(false);
    QVERIFY(!m_searchWidget->isHighlightAll());
    
    // Test setting highlight all
    m_searchWidget->setHighlightAll(true);
    QVERIFY(m_searchWidget->isHighlightAll());
}

void TestSearchWidget::testSearchFieldFocus()
{
    // Create a parent widget to ensure proper focus handling
    QWidget parentWidget;
    m_searchWidget->setParent(&parentWidget);
    parentWidget.show();
    
    // Focus the search field
    m_searchWidget->focusSearchField();
    
    // Find the search edit field
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);
    
    // Verify focus (may not work in headless test environment)
    // QVERIFY(searchEdit->hasFocus());
    
    // Test that text is selected when focusing
    searchEdit->setText("test text");
    m_searchWidget->focusSearchField();
    QVERIFY(searchEdit->hasSelectedText());
}

void TestSearchWidget::testPerformSearch()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);
    
    // Set search term and perform search
    m_searchWidget->setSearchTerm("test search");
    m_searchWidget->performSearch();
    
    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("test search"));
    QCOMPARE(arguments.at(1).toBool(), false); // case sensitive
    QCOMPARE(arguments.at(2).toBool(), false); // whole words
}

void TestSearchWidget::testPerformSearchWithEmptyTerm()
{
    QSignalSpy searchSpy(m_searchWidget, &SearchWidget::searchRequested);
    QSignalSpy clearSpy(m_searchWidget, &SearchWidget::searchCleared);
    
    // Perform search with empty term
    m_searchWidget->setSearchTerm("");
    m_searchWidget->performSearch();
    
    // Should not emit search signal, but should emit clear signal
    QCOMPARE(searchSpy.count(), 0);
    QCOMPARE(clearSpy.count(), 1);
}

void TestSearchWidget::testClearSearch()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchCleared);
    
    // Set some search term and results
    m_searchWidget->setSearchTerm("test");
    m_searchWidget->setSearchResults(2, 5);
    
    // Clear search
    m_searchWidget->clearSearch();
    
    QVERIFY(m_searchWidget->getSearchTerm().isEmpty());
    QCOMPARE(spy.count(), 1);
}

void TestSearchWidget::testSearchSignalEmission()
{
    QSignalSpy searchSpy(m_searchWidget, &SearchWidget::searchRequested);
    QSignalSpy clearSpy(m_searchWidget, &SearchWidget::searchCleared);
    QSignalSpy nextSpy(m_searchWidget, &SearchWidget::findNext);
    QSignalSpy prevSpy(m_searchWidget, &SearchWidget::findPrevious);
    
    // Test search signal
    m_searchWidget->setSearchTerm("test");
    m_searchWidget->performSearch();
    QCOMPARE(searchSpy.count(), 1);
    
    // Test clear signal
    m_searchWidget->clearSearch();
    QCOMPARE(clearSpy.count(), 1);
    
    // Test navigation signals (would need to find and click buttons)
    // This is more complex in a unit test environment
    QVERIFY(true); // Placeholder for navigation signal tests
}

void TestSearchWidget::testSearchWithOptions()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);
    
    // Set search options
    m_searchWidget->setSearchTerm("Test");
    m_searchWidget->setCaseSensitive(true);
    m_searchWidget->setWholeWords(true);
    
    m_searchWidget->performSearch();
    
    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("Test"));
    QCOMPARE(arguments.at(1).toBool(), true);  // case sensitive
    QCOMPARE(arguments.at(2).toBool(), true);  // whole words
}

void TestSearchWidget::testSearchInProgress()
{
    // Test initial state
    QVERIFY(!m_searchWidget->isSearchInProgress());
    
    // Set search in progress
    m_searchWidget->setSearchInProgress(true);
    QVERIFY(m_searchWidget->isSearchInProgress());
    
    // Clear search in progress
    m_searchWidget->setSearchInProgress(false);
    QVERIFY(!m_searchWidget->isSearchInProgress());
}

void TestSearchWidget::testSearchResults()
{
    // Set search results
    m_searchWidget->setSearchResults(2, 10);
    
    // Verify results are set (internal state testing would require access to private members)
    // We can test this indirectly through UI updates
    QVERIFY(true); // Placeholder - actual testing would require UI inspection
}

void TestSearchWidget::testClearSearchResults()
{
    // Set some results first
    m_searchWidget->setSearchResults(5, 20);

    // Clear results
    m_searchWidget->clearSearchResults();

    // Verify results are cleared (would need UI inspection for full verification)
    QVERIFY(true); // Placeholder
}

void TestSearchWidget::testSearchResultsDisplay()
{
    // Test search results display functionality
    m_searchWidget->setSearchResults(1, 5);

    // Find the results label to verify display
    QLabel* resultsLabel = m_searchWidget->findChild<QLabel*>();
    if (resultsLabel) {
        // Check that results are displayed (basic test)
        QVERIFY(!resultsLabel->text().isEmpty());
    }

    // Test no results case
    m_searchWidget->setSearchResults(0, 0);
    if (resultsLabel) {
        // Should show "No results" or be empty
        QVERIFY(true); // Placeholder for actual text verification
    }

    // Clear results
    m_searchWidget->clearSearchResults();
    if (resultsLabel) {
        // Should be cleared
        QVERIFY(true); // Placeholder
    }
}

void TestSearchWidget::simulateUserTyping(const QString& text, int delayMs)
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    if (searchEdit) {
        searchEdit->clear();
        for (const QChar& ch : text) {
            searchEdit->setText(searchEdit->text() + ch);
            QTest::qWait(delayMs);
            QApplication::processEvents();
        }
    }
}

void TestSearchWidget::waitForAutoSearch(int timeoutMs)
{
    QTimer timeout;
    timeout.setSingleShot(true);
    timeout.start(timeoutMs);
    
    QEventLoop loop;
    connect(&timeout, &QTimer::timeout, &loop, &QEventLoop::quit);
    loop.exec();
}

bool TestSearchWidget::hasSearchOption(const QString& optionName)
{
    QCheckBox* checkbox = m_searchWidget->findChild<QCheckBox*>();
    return checkbox && checkbox->text().contains(optionName, Qt::CaseInsensitive);
}

void TestSearchWidget::testAutoSearchEnabled()
{
    // Test initial state (should be enabled by default)
    QVERIFY(m_searchWidget->isAutoSearchEnabled());

    // Test disabling auto-search
    m_searchWidget->setAutoSearchEnabled(false);
    QVERIFY(!m_searchWidget->isAutoSearchEnabled());

    // Test enabling auto-search
    m_searchWidget->setAutoSearchEnabled(true);
    QVERIFY(m_searchWidget->isAutoSearchEnabled());
}

void TestSearchWidget::testAutoSearchDisabled()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);

    // Disable auto-search
    m_searchWidget->setAutoSearchEnabled(false);

    // Simulate typing
    simulateUserTyping("test", 10);

    // Wait longer than default auto-search delay
    waitForAutoSearch(1000);

    // Should not have triggered auto-search
    QCOMPARE(spy.count(), 0);
}

void TestSearchWidget::testAutoSearchDelay()
{
    // Test default delay
    QCOMPARE(m_searchWidget->getAutoSearchDelay(), 500);

    // Test setting custom delay
    m_searchWidget->setAutoSearchDelay(1000);
    QCOMPARE(m_searchWidget->getAutoSearchDelay(), 1000);

    // Test minimum delay enforcement
    m_searchWidget->setAutoSearchDelay(50); // Below minimum
    QCOMPARE(m_searchWidget->getAutoSearchDelay(), 100); // Should be clamped to minimum
}

void TestSearchWidget::testAutoSearchTimeout()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);

    // Set short auto-search delay
    m_searchWidget->setAutoSearchDelay(200);
    m_searchWidget->setAutoSearchEnabled(true);

    // Simulate typing
    simulateUserTyping("auto", 10);

    // Wait for auto-search to trigger
    waitForAutoSearch(500);

    // Should have triggered auto-search
    QVERIFY(spy.count() > 0);
}

void TestSearchWidget::testAutoSearchCancellation()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);

    // Set longer auto-search delay
    m_searchWidget->setAutoSearchDelay(500);
    m_searchWidget->setAutoSearchEnabled(true);

    // Start typing
    simulateUserTyping("can", 10);

    // Wait a bit but not long enough for auto-search
    QTest::qWait(200);

    // Continue typing (should cancel previous timer)
    simulateUserTyping("cel", 10);

    // Wait for auto-search
    waitForAutoSearch(800);

    // Should only have one search request (for "cancel")
    QVERIFY(spy.count() <= 1);
}

void TestSearchWidget::testSearchHistoryIntegration()
{
    // Verify history is set
    QCOMPARE(m_searchWidget->getSearchHistory(), m_searchHistory);

    // Perform a search
    m_searchWidget->setSearchTerm("history test");
    m_searchWidget->performSearch();

    // Verify search was added to history
    QList<SearchHistoryEntry> history = m_searchHistory->getHistory();
    QVERIFY(history.size() > 0);
    QCOMPARE(history.last().searchTerm, QString("history test"));
}

void TestSearchWidget::testHistoryItemSelection()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::historyItemSelected);

    // Add some history
    m_searchHistory->addSearch("previous search", true, false);

    // Create history entry
    SearchHistoryEntry entry("previous search", true, false);

    // Select history item
    m_searchWidget->selectHistoryItem(entry);

    // Verify search term and options were set
    QCOMPARE(m_searchWidget->getSearchTerm(), QString("previous search"));
    QVERIFY(m_searchWidget->isCaseSensitive());
    QVERIFY(!m_searchWidget->isWholeWords());

    // Verify signal was emitted
    QCOMPARE(spy.count(), 1);
}

void TestSearchWidget::testHistoryMenuCreation()
{
    // Add some history entries
    m_searchHistory->addSearch("search 1", false, false);
    m_searchHistory->addSearch("search 2", true, false);
    m_searchHistory->addSearch("search 3", false, true);

    // Show history menu (this would create the menu)
    m_searchWidget->showSearchHistory();

    // Verify menu exists (would need access to private members for full verification)
    QVERIFY(true); // Placeholder
}

void TestSearchWidget::testHistoryCompleter()
{
    // Add history entries
    m_searchHistory->addSearch("complete me", false, false);
    m_searchHistory->addSearch("completion test", false, false);

    // Get completer
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    QCompleter* completer = searchEdit->completer();
    QVERIFY(completer != nullptr);

    // Test completion (would need more complex setup for full testing)
    QVERIFY(true); // Placeholder
}

void TestSearchWidget::testHistorySignals()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::historyItemSelected);

    // Create and select history entry
    SearchHistoryEntry entry("signal test", false, true);
    m_searchWidget->selectHistoryItem(entry);

    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("signal test"));
    QCOMPARE(arguments.at(1).toBool(), false); // case sensitive
    QCOMPARE(arguments.at(2).toBool(), true);  // whole words
}

void TestSearchWidget::testUIComponentsCreation()
{
    // Test that essential UI components exist
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    QList<QPushButton*> buttons = m_searchWidget->findChildren<QPushButton*>();
    QVERIFY(buttons.size() >= 2); // At least search and clear buttons

    QList<QCheckBox*> checkboxes = m_searchWidget->findChildren<QCheckBox*>();
    QVERIFY(checkboxes.size() >= 3); // Case sensitive, whole words, highlight all
}

void TestSearchWidget::testButtonStates()
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Find search and clear buttons
    QList<QPushButton*> buttons = m_searchWidget->findChildren<QPushButton*>();
    QPushButton* searchButton = nullptr;
    QPushButton* clearButton = nullptr;

    for (QPushButton* button : buttons) {
        if (button->text().contains("Search", Qt::CaseInsensitive)) {
            searchButton = button;
        } else if (button->text().contains("Clear", Qt::CaseInsensitive)) {
            clearButton = button;
        }
    }

    if (searchButton && clearButton) {
        // Test initial state (empty search field)
        searchEdit->clear();
        QApplication::processEvents();
        QVERIFY(!searchButton->isEnabled());
        QVERIFY(!clearButton->isEnabled());

        // Test with text
        searchEdit->setText("test");
        QApplication::processEvents();
        QVERIFY(searchButton->isEnabled());
        QVERIFY(clearButton->isEnabled());
    }
}

void TestSearchWidget::testNavigationButtons()
{
    // Set some search results
    m_searchWidget->setSearchResults(2, 5);

    // Find navigation buttons (would need access to private members for full testing)
    QList<QPushButton*> buttons = m_searchWidget->findChildren<QPushButton*>();

    // Verify navigation buttons exist and are properly configured
    QVERIFY(buttons.size() > 0); // Placeholder test
}

void TestSearchWidget::testProgressIndicator()
{
    // Test search in progress state
    m_searchWidget->setSearchInProgress(true);

    // Find progress bar
    QProgressBar* progressBar = m_searchWidget->findChild<QProgressBar*>();
    if (progressBar) {
        // Progress bar should be visible during search
        QVERIFY(progressBar->isVisible());
    }

    // Test search completed state
    m_searchWidget->setSearchInProgress(false);

    if (progressBar) {
        // Progress bar should be hidden when not searching
        QVERIFY(!progressBar->isVisible());
    }
}

void TestSearchWidget::testOptionsCheckboxes()
{
    QList<QCheckBox*> checkboxes = m_searchWidget->findChildren<QCheckBox*>();

    // Should have at least 3 checkboxes
    QVERIFY(checkboxes.size() >= 3);

    // Test that checkboxes respond to programmatic changes
    m_searchWidget->setCaseSensitive(true);
    m_searchWidget->setWholeWords(true);
    m_searchWidget->setHighlightAll(false);

    // Find and verify checkbox states
    for (QCheckBox* checkbox : checkboxes) {
        if (checkbox->text().contains("Case", Qt::CaseInsensitive)) {
            QVERIFY(checkbox->isChecked());
        } else if (checkbox->text().contains("Whole", Qt::CaseInsensitive)) {
            QVERIFY(checkbox->isChecked());
        } else if (checkbox->text().contains("Highlight", Qt::CaseInsensitive)) {
            QVERIFY(!checkbox->isChecked());
        }
    }
}

void TestSearchWidget::testWidgetEnabled()
{
    // Test initial state
    QVERIFY(m_searchWidget->isEnabled());

    // Test disabling widget
    m_searchWidget->setEnabled(false);
    QVERIFY(!m_searchWidget->isEnabled());

    // Test enabling widget
    m_searchWidget->setEnabled(true);
    QVERIFY(m_searchWidget->isEnabled());
}

void TestSearchWidget::testWidgetVisible()
{
    // Test initial state
    QVERIFY(m_searchWidget->isVisible());

    // Test hiding widget
    m_searchWidget->setVisible(false);
    QVERIFY(!m_searchWidget->isVisible());

    // Test showing widget
    m_searchWidget->setVisible(true);
    QVERIFY(m_searchWidget->isVisible());
}

void TestSearchWidget::testSearchFieldPlaceholder()
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Verify placeholder text is set
    QVERIFY(!searchEdit->placeholderText().isEmpty());
    QVERIFY(searchEdit->placeholderText().contains("Search", Qt::CaseInsensitive));
}

void TestSearchWidget::testSearchTextChanged()
{
    QSignalSpy clearSpy(m_searchWidget, &SearchWidget::searchCleared);

    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Set text and clear it
    searchEdit->setText("test");
    QApplication::processEvents();

    searchEdit->clear();
    QApplication::processEvents();

    // Should emit searchCleared when text becomes empty
    QVERIFY(clearSpy.count() > 0);
}

void TestSearchWidget::testSearchTextEdited()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);

    // Enable auto-search with short delay
    m_searchWidget->setAutoSearchEnabled(true);
    m_searchWidget->setAutoSearchDelay(100);

    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Simulate user editing text
    searchEdit->setText("edit");
    QApplication::processEvents();

    // Wait for auto-search
    waitForAutoSearch(300);

    // Should trigger auto-search
    QVERIFY(spy.count() > 0);
}

void TestSearchWidget::testSearchOptionsChanged()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchOptionsChanged);

    // Change search options
    m_searchWidget->setCaseSensitive(true);
    m_searchWidget->setWholeWords(true);
    m_searchWidget->setHighlightAll(false);

    // Should emit options changed signal
    QVERIFY(spy.count() > 0);
}

void TestSearchWidget::testCompleterActivation()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);

    // Add history for completion
    m_searchHistory->addSearch("completer test", false, false);

    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    QCompleter* completer = searchEdit->completer();
    QVERIFY(completer != nullptr);

    // Simulate completer activation (this is complex to test directly)
    // We'll test the method directly instead
    m_searchWidget->setSearchTerm("completer test");

    // Verify search term was set
    QCOMPARE(m_searchWidget->getSearchTerm(), QString("completer test"));
}

void TestSearchWidget::testSearchStyling()
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Test normal state styling
    m_searchWidget->clearSearchResults();
    QString normalStyle = searchEdit->styleSheet();

    // Test found results styling
    m_searchWidget->setSearchTerm("test");
    m_searchWidget->setSearchResults(1, 5);
    QString foundStyle = searchEdit->styleSheet();

    // Styles should be different (or at least not cause crashes)
    QVERIFY(true); // Placeholder - actual style comparison would be implementation-specific
}

void TestSearchWidget::testFoundResultsStyling()
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Set search term and results
    m_searchWidget->setSearchTerm("found");
    m_searchWidget->setSearchResults(2, 10);

    // Verify styling is applied (implementation-specific)
    QVERIFY(true); // Placeholder
}

void TestSearchWidget::testNotFoundStyling()
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Set search term with no results
    m_searchWidget->setSearchTerm("notfound");
    m_searchWidget->setSearchResults(-1, 0);

    // Verify not-found styling is applied
    QVERIFY(true); // Placeholder
}

void TestSearchWidget::testSearchingStateStyling()
{
    QLineEdit* searchEdit = m_searchWidget->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);

    // Set search in progress
    m_searchWidget->setSearchInProgress(true);

    // Verify searching state styling
    QVERIFY(true); // Placeholder

    // Clear search in progress
    m_searchWidget->setSearchInProgress(false);
}

void TestSearchWidget::testEmptySearchHandling()
{
    QSignalSpy searchSpy(m_searchWidget, &SearchWidget::searchRequested);
    QSignalSpy clearSpy(m_searchWidget, &SearchWidget::searchCleared);

    // Perform search with empty term
    m_searchWidget->setSearchTerm("");
    m_searchWidget->performSearch();

    // Should not emit search signal
    QCOMPARE(searchSpy.count(), 0);
    // Should emit clear signal
    QCOMPARE(clearSpy.count(), 1);
}

void TestSearchWidget::testLongSearchTerms()
{
    QString longTerm(10000, 'X'); // Very long search term

    // Set long search term
    m_searchWidget->setSearchTerm(longTerm);

    // Should handle gracefully without crashing
    QCOMPARE(m_searchWidget->getSearchTerm(), longTerm);

    // Perform search with long term
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);
    m_searchWidget->performSearch();

    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), longTerm);
}

void TestSearchWidget::testSpecialCharacters()
{
    QString specialTerm = "test@#$%^&*()[]{}|\\:;\"'<>?,./~`";

    // Set search term with special characters
    m_searchWidget->setSearchTerm(specialTerm);
    QCOMPARE(m_searchWidget->getSearchTerm(), specialTerm);

    // Perform search
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);
    m_searchWidget->performSearch();

    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), specialTerm);
}

void TestSearchWidget::testSearchWithoutHistory()
{
    // Create widget without history
    SearchWidget* widgetNoHistory = new SearchWidget(this);

    // Should not crash when performing search without history
    widgetNoHistory->setSearchTerm("no history test");
    widgetNoHistory->performSearch();

    // Should work normally
    QCOMPARE(widgetNoHistory->getSearchTerm(), QString("no history test"));

    delete widgetNoHistory;
}

void TestSearchWidget::testSearchWorkflow()
{
    QSignalSpy searchSpy(m_searchWidget, &SearchWidget::searchRequested);
    QSignalSpy clearSpy(m_searchWidget, &SearchWidget::searchCleared);

    // Complete search workflow
    m_searchWidget->setSearchTerm("workflow test");
    m_searchWidget->setCaseSensitive(true);
    m_searchWidget->setWholeWords(false);

    // Perform search
    m_searchWidget->performSearch();
    QCOMPARE(searchSpy.count(), 1);

    // Set results
    m_searchWidget->setSearchResults(3, 15);

    // Clear search
    m_searchWidget->clearSearch();
    QCOMPARE(clearSpy.count(), 1);
    QVERIFY(m_searchWidget->getSearchTerm().isEmpty());
}

void TestSearchWidget::testHistoryWorkflow()
{
    // Add searches to history
    m_searchWidget->setSearchTerm("history 1");
    m_searchWidget->performSearch();

    m_searchWidget->setSearchTerm("history 2");
    m_searchWidget->setCaseSensitive(true);
    m_searchWidget->performSearch();

    // Verify history contains searches
    QList<SearchHistoryEntry> history = m_searchHistory->getHistory();
    QVERIFY(history.size() >= 2);

    // Select history item
    SearchHistoryEntry entry = history.first();
    m_searchWidget->selectHistoryItem(entry);

    // Verify search term and options were restored
    QCOMPARE(m_searchWidget->getSearchTerm(), entry.searchTerm);
    QCOMPARE(m_searchWidget->isCaseSensitive(), entry.caseSensitive);
    QCOMPARE(m_searchWidget->isWholeWords(), entry.wholeWords);
}

void TestSearchWidget::testAutoSearchWorkflow()
{
    QSignalSpy spy(m_searchWidget, &SearchWidget::searchRequested);

    // Configure auto-search
    m_searchWidget->setAutoSearchEnabled(true);
    m_searchWidget->setAutoSearchDelay(200);

    // Simulate typing
    simulateUserTyping("auto search", 50);

    // Wait for auto-search
    waitForAutoSearch(500);

    // Should have triggered auto-search
    QVERIFY(spy.count() > 0);

    // Verify search term
    QList<QVariant> arguments = spy.takeLast();
    QCOMPARE(arguments.at(0).toString(), QString("auto search"));
}

QTEST_MAIN(TestSearchWidget)
#include "test_search_widget.moc"
