#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include "ElaIntegration.h"

class QTabWidget;
class QSpinBox;
class QDoubleSpinBox;
class QCheckBox;
class QComboBox;
class QSlider;
class QLabel;
class QGroupBox;
class QPushButton;

class SettingsDialog : public ElaContentDialog
{
    friend class TestSettingsDialog;
    friend class TestIntegration;
    Q_OBJECT

public:
    explicit SettingsDialog(QWidget *parent = nullptr);

    // Getters for settings values
    double getDefaultZoom() const;
    int getCacheSize() const;
    int getPreloadRange() const;
    bool getShowMemoryUsage() const;
    bool getShowThumbnails() const;
    QString getExportFormat() const;
    int getExportQuality() const;
    bool getRememberWindowState() const;
    bool getAutoFitOnOpen() const;
    int getThemeMode() const;

    // Setters for current values
    void setDefaultZoom(double zoom);
    void setCacheSize(int sizeMB);
    void setPreloadRange(int range);
    void setShowMemoryUsage(bool show);
    void setShowThumbnails(bool show);
    void setExportFormat(const QString& format);
    void setExportQuality(int quality);
    void setRememberWindowState(bool remember);
    void setAutoFitOnOpen(bool autoFit);
    void setThemeMode(int themeMode);

private slots:
    void resetToDefaults();
    void onCacheSizeChanged(int value);

private:
    void createGeneralTab();
    void createPerformanceTab();
    void createExportTab();
    void createInterfaceTab();
    void updateCacheSizeLabel(int sizeMB);

    ElaTab* m_tabWidget;

    // General settings
    ElaDoubleSpin* m_defaultZoomSpinBox;
    ElaCheck* m_autoFitOnOpenCheckBox;
    ElaCheck* m_rememberWindowStateCheckBox;

    // Performance settings
    ElaSpin* m_cacheSizeSpinBox;
    ElaLabel* m_cacheSizeLabel;
    ElaSpin* m_preloadRangeSpinBox;
    ElaCheck* m_showMemoryUsageCheckBox;

    // Export settings
    ElaCombo* m_exportFormatComboBox;
    ElaSliderWidget* m_exportQualitySlider;
    ElaLabel* m_exportQualityLabel;

    // Interface settings
    ElaCombo* m_themeModeComboBox;
    ElaCheck* m_showThumbnailsCheckBox;

    ElaButton* m_resetButton;
};

#endif // SETTINGSDIALOG_H
